import ASYNC from 'async';
import MOMENT from 'moment';
import utility from '../lib';
import digitalUtility from 'digital-in-util'
import _ from 'lodash'
import OS from 'os'

class publishToKafka {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.commonLib = new utility.commonLib(options);
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.oldBillFetchDueDateAllowedService = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'ALLOWED_SERVICES'], ['electricity']);
        this.oldBillFetchDueDateBlacklistedOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'BLACKLISTED_OPERATORS'], ['electricity']);
    }

    publishToNonPaytmBillsConsumer(done, payload, source, ref) {
        let self = this;
        try {
            ref.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', ''),
                messages: JSON.stringify(payload)
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:PUBLISH_TO_NON_PAYTM_BILLS_CONSUMER",
                        `SERVICE:${_.get(payload, 'service', null)} `,
                        'STATUS:ERROR',
                        `OPERATOR:${_.get(payload, 'operator', 'NO_OPERATOR')}`,
                        "TYPE:NON_PAYTM_EVENTS",
                        "TOPIC:NON_PAYTM_RECORDS_DWH",
                        "SOURCE:" + source
                    ]);
                    self.L.critical('PUBLISH_TO_NON_PAYTM_BILLS_CONSUMER :: nonPaytmKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                    return done('Error while publishing message in Kafka');
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:PUBLISH_TO_NON_PAYTM_BILLS_CONSUMER",
                        `SERVICE:${_.get(payload, 'service', null)} `,
                        'STATUS:PUBLISHED',
                        "TYPE:NON_PAYTM_EVENTS",
                        "TOPIC:NON_PAYTM_RECORDS_DWH",
                        `OPERATOR: ${_.get(payload, 'operator', 'NO_OPERATOR')}`,
                        "SOURCE:" + source
                    ]);
                    self.L.log('PUBLISH_TO_NON_PAYTM_BILLS_CONSUMER :: nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS', JSON.stringify(payload));
                    return done(null);
                }
            })
        } catch (error) {
            self.L.error(" updateCassandra error:", error);
            //if (self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate == null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "NON_RU"), `updateCassandra error: ${error} `, done);
            return done(error);
        }
    }

    publishToAutomaticSync(done, record, ref) {
        let self = this,
            dbData = _.get(record, 'dbData', []);
        self.L.log('publishToAutomaticSync:: starting publishToAutomaticSync');

        if(_.get(record, 'skipNotificationWhenUPMSBillAlreadyinDB', false)){
            self.L.log('publishToAutomaticSync:: Skipping publish to automatic sync as bill with same due date is in DB');
            return done(null);
        }

        if (!record.service || !record.operator || !record.rechargeNumber || !record.productId) {
            self.L.critical('publishInKafka :: invalid inputs ', record.service, record.operator, record.rechargeNumber, record.productId);
            return done('invalid inputs');
        }

        ASYNC.eachLimit(dbData, 3, (dataRow, cb) => {
            let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
            if (dataRow.status == 13 || dataRow.status == 7) {
                self.L.log('publishInKafka', `Skipping pulish to : AUTOMATIC_SYNC_TOPIC for ${record.debugKey}, dbRow::${dbDebugKey} | due to inactice/old record`);
                return cb();
            }

            let billsData = record.billsData;

            dataRow.due_date = MOMENT(_.get(billsData, 'commonDueDate', record.billDueDate), 'YYYY-MM-DD HH:mm:ss').startOf('day').format('YYYY-MM-DD HH:mm:ss');
            if (record.is_automatic && record.is_automatic !== 0) {
                dataRow.is_automatic = record.is_automatic;
            }
            dataRow.bill_date = record.billDate ? MOMENT(record.billDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : null;
            dataRow.amount = record.amount;
            dataRow.bill_fetch_date = _.get(billsData, 'billFetchDate', null);
            dataRow.next_bill_fetch_date = _.get(billsData, 'nextBillFetchDate', null);
            dataRow.status = _.get(billsData, 'commonStatus', 0);
            dataRow.extra = _.get(billsData, 'extra', null);
            dataRow.updated_at = MOMENT().format('YYYY-MM-DD HH:mm:ss');
            dataRow.customerOtherInfo = _.get(billsData, 'customerOtherInfo', null);

            let row = self.commonLib.mapBillsTableColumns(dataRow);
            row.billGen = true;
            row.source = _.get(record, 'source', 'UNKNOWN');
            row.machineId = OS.hostname();
            if (_.get(row, 'is_automatic', 0) !== 0 && _.get(row, 'is_automatic', 0) !== 5) {
                ref.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.AUTOMATIC_SYNC.AUTOMATIC_SYNC_TOPIC', ''),
                    messages: JSON.stringify(row)
                }], function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE: PUBLISH_TO_AUTOMATIC_SYNC",
                            `SERVICE:${_.get(record, 'service', null)}`,
                            'STATUS:ERROR',
                            'TYPE:KAFKA_PUBLISH',
                            'TOPIC:AUTOMATIC_SYNC',
                            "OPERATOR:" + record.operator
                        ]);
                        self.L.critical('Error while publishing message in Kafka - MSG:- ' + JSON.stringify(row), error);
                    } else {
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:PUBLISH_TO_AUTOMATIC_SYNC",
                            `SERVICE:${_.get(record, 'service', null)}`,
                            'STATUS:PUBLISHED',
                            'TYPE:KAFKA_PUBLISH',
                            'TOPIC:AUTOMATIC_SYNC',
                            "OPERATOR:" + record.operator
                        ]);
                        self.L.log('Message published successfully in Kafka', ' on topic AUTOMATIC_SYNC', JSON.stringify(row));
                    }
                    return cb();
                }, [200, 800]);
            }
            else {
                self.L.log('Skipping publish in Kafka', ' on topic AUTOMATIC_SYNC', 'Due to is_automatic = 0');
                return cb();
            }
        }, (error, res) => {
            if (error) {
                self.L.error("postpaidSmsParsing :: publishKafka ", "Error occured", error);
            }
            return done(error);
        });
    }

    publishInBillFetchKafka(done, processedRecord, ref, refRealTime) {
        let self = this;
        let dbData = _.get(processedRecord, 'dbData', []);
        let billsData = processedRecord.billsData;
        self.L.log(`publishInBillFetchKafka:: Record Category - ${processedRecord.service}`);

        if(_.get(processedRecord, 'skipNotificationWhenUPMSBillAlreadyinDB', false)){
            self.L.log('publishInBillFetchKafka:: Skipping publish to bill fetch kafka as bill with same due date is in DB');
            return done(null);
        }

        ASYNC.eachLimit(dbData, 3, (dataRow, cb) => {
            let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;

            if (dataRow.notification_status == 0) {
                self.L.error(`stop publishing data on REMINDER_BILL_FETCH_REALTIME via the Kafka pipeline for notification status : ${dataRow.notification_status} debugKey::`, dbDebugKey);
                return cb();
            }

            if (dataRow.is_automatic != 0 && dataRow.is_automatic != 5) {
                self.L.error(`stop publishing data on REMINDER_BILL_FETCH_REALTIME via the Kafka pipeline for automatic status : ${dataRow.is_automatic} debugKey::`, dbDebugKey);
                return cb();
            }

            let payload = {
                source: "reminderBillFetch",
                notificationType: "BILLGEN",
                data: {
                    id: dataRow.id,
                    customerId: dataRow.customer_id,
                    rechargeNumber: dataRow.recharge_number,
                    productId: processedRecord.productId,
                    operator: processedRecord.operator,
                    amount: processedRecord.amount,
                    billDate: _.get(billsData, 'billDate', _.get(processedRecord, 'billDate', MOMENT())),
                    dueDate: _.get(billsData, 'commonDueDate', processedRecord.billDueDate),
                    billFetchDate: _.get(processedRecord, 'billFetchDate', MOMENT()),
                    nextBillFetchDate: _.get(billsData, 'nextBillFetchDate', MOMENT()),
                    paytype: _.get(dataRow, 'paytype', 'postpaid'),
                    gateway: _.get(dataRow, 'gateway', 'upms'),
                    service: _.get(dataRow, 'service', processedRecord.service),
                    circle: _.get(dataRow, 'circle', processedRecord.circle),
                    customerMobile: dataRow.customer_mobile,
                    customerEmail: dataRow.customer_email,
                    status: self.config.COMMON.bills_status.BILL_FETCHED,
                    extra: _.get(billsData, 'extra', null),
                    userData: _.get(dataRow, 'userData', null),
                    notification_status: dataRow.notification_status,
                    customerOtherInfo: _.get(dataRow, 'customer_other_info', null),
                    is_automatic: dataRow.is_automatic,
                    service_id: _.get(dataRow, 'service_id', null),
                    paymentDate: _.get(dataRow, 'payment_date', null),
                    remindLaterDate: _.get(billsData, 'remindLaterDate', null)
                }
            }

            if(processedRecord.status == _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5) &&
            self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(_.get(processedRecord, 'service', null))) > -1 && 
            !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(_.get(processedRecord, 'operator', null))) > -1) 
            && _.get(processedRecord, 'oldBillFetchDate', null) == MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss')){
                _.set(payload, "notificationType", "OLD_BILL_NOTIFICATION");
                _.set(payload, ["data", "status"], _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5));
            }

            utility.sendNotificationMetricsFromSource(payload)
            let toBeNotifiedRealtime = self.commonLib.decideTopicToPublishBillGen();
            if (!toBeNotifiedRealtime) {
                utility.sendNotificationMetricsFromSource(payload)
                ref.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
                    messages: JSON.stringify(payload)
                }], function (error) {
                    if (error) {
                        utility.sendNotificationMetricsFromSource(payload, "ERROR")
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:BILLGEN_NOTIFICATION',
                            'STATUS:ERROR',
                            'TYPE:ERROR_WHILE_PUBLISHING',
                            'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
                            'OPERATOR:' + processedRecord.operator,
                            `SOURCE:${_.get(processedRecord, 'source', 'UPMS')}`
                        ]);
                        self.L.critical('prepareKafkaResponse :: kafkaBillFetchPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                    } else {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:BILLGEN_NOTIFICATION',
                            'STATUS:SUCCESS',
                            'TYPE:PUBLISHED',
                            'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
                            'OPERATOR:' + processedRecord.operator,
                            `SOURCE:${_.get(processedRecord, 'source', 'UPMS')}`
                        ])
                        self.L.log('prepareKafkaResponse :: kafkaBillFetchPublisher', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH', JSON.stringify(payload));
                    }
                    return cb();
                }, [200, 800]);
            }
            else {
                payload.source = 'BillGenPublisherRealTime';
                utility.sendNotificationMetricsFromSource(payload)
                refRealTime.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', ''),
                    messages: JSON.stringify(payload)
                }], function (error) {
                    if (error) {
                        utility.sendNotificationMetricsFromSource(payload, "ERROR")
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:PUBLISHER_BILLGEN_NOTIFICATION", 'STATUS:ERROR', 'TYPE:REMINDER_BILL_FETCH', "OPERATOR:" + payload.operator, `SOURCE:${_.get(processedRecord, 'source', 'UPMS')}`]);
                        self.L.critical('publishInKafka :: kafkaBillFetchPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                    } else {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:PUBLISHER_BILLGEN_NOTIFICATION", 'STATUS:PUBLISHED', 'TYPE:REMINDER_BILL_FETCH', "OPERATOR:" + payload.operator, `SOURCE:${_.get(processedRecord, 'source', 'UPMS')}`]);
                        self.L.log('prepareKafkaResponse :: kafkaBillFetchPublisher', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH_REALTIME', JSON.stringify(payload));
                    }
                    return cb();
                }, [200, 800]);
            }
        }, (error, res) => {
            if (error) {
                self.L.error("postpaidSmsParsing :: publish in BillFetchKafka ", "Error occured", error);
            }
            return done(error);
        });
    }

    publishCtEvents(done, record, ref) {
        let self = this;
        self.L.log(`publishCtEvents:: Record Category - ${record.service}`);
        const productId = _.get(record, 'productId', 0);
        let dbData = _.get(record, 'dbData', []);
        ASYNC.eachLimit(dbData, 3, (dataRow, cb) => {
            let eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'BILLGEN'], 'reminderBillGen');

            let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
            if ((dataRow.status == 13 || dataRow.status == 7)) {
                self.L.log('publishInKafka', `Skipping pulish CT for ${record.debugKey}, dbRow::${dbDebugKey} | due to inactice record`);
                return cb();
            }

            if (self.commonLib.isCTEventBlocked(eventName)) {
                self.L.info(`Blocking CT event ${eventName}`)
                return cb()
            }

            let billsData = record.billsData;
            dataRow.due_date = MOMENT(record.dueDate, 'YYYY-MM-DD HH:mm:ss').startOf('day').format('YYYY-MM-DD HH:mm:ss');

            if (dataRow.notification_status == 0) {
                self.L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${dataRow.notification_status} debugKey::`, dbDebugKey);
                return cb();
            }

            if (dataRow.notification_status == null)
                dataRow.notification_status = 1
            dataRow.bill_date = record.billDate ? MOMENT.utc(record.billDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : null;
            dataRow.amount = record.amount;
            dataRow.bill_fetch_date = MOMENT.utc(_.get(billsData, 'billFetchDate', null)).format('YYYY-MM-DD HH:mm:ss');
            dataRow.next_bill_fetch_date = MOMENT.utc(_.get(billsData, 'nextBillFetchDate', null)).format('YYYY-MM-DD HH:mm:ss');
            dataRow.status = _.get(billsData, 'commonStatus', 0);
            dataRow.extra = _.get(billsData, 'extra', null);
            dataRow.updated_at = MOMENT.utc().format('YYYY-MM-DD HH:mm:ss');
            dataRow.customerOtherInfo = _.get(billsData, 'customerOtherInfo', null);
            dataRow.remindLaterDate = _.get(billsData, 'remindLaterDate', null);
            ASYNC.waterfall([
                next => {
                    self.commonLib.getRetailerData((error) => {
                        if (error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, dataRow.customer_id, dataRow);
                },
                next => {
                    self.commonLib.getCvrData((error) => {
                        if (error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, productId, dataRow);
                },
                next => {
                    let mappedData = self.reminderUtils.createCTPipelinePayload(dataRow, eventName, dbDebugKey);
                    let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                    ref.publishData([{
                        topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                        messages: JSON.stringify(mappedData)
                    }], (error) => {
                        if (error) {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:PUBLISH_TO_CT_EVENTS",
                                `SERVICE:${_.get(record, 'service', null)}`,
                                'STATUS:ERROR',
                                "TYPE:KAFKA_PUBLISH",
                                "TOPIC:CT_EVENTS",
                                "OPERATOR:" + dataRow.operator,
                                `SOURCE:${record.source}`
                            ]);
                            self.L.critical('PUBLISH_TO_CT_EVENTS :: publishCtEvents', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(clonedData), error);
                        } else {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:PUBLISH_TO_CT_EVENTS",
                                `SERVICE:${_.get(record, 'service', null)}`,
                                'STATUS:PUBLISHED',
                                "TYPE:KAFKA_PUBLISH",
                                "TOPIC:CT_EVENTS",
                                "OPERATOR:" + dataRow.operator,
                                `SOURCE:${record.source}`,
                                `EVENT_NAME:${eventName}`
                            ]);
                            self.L.log('PUBLISH_TO_CT_EVENTS :: publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                        }
                        return next(error);
                    }, [200, 800]);
                }
            ], (err) => {
                if (err)
                    self.L.log('PUBLISH_TO_CT_EVENTS :: publishCtEvents', 'Error while publishing message in Kafka: ', err)
                return cb(err)
            })
        }, (error, res) => {
            if (error) {
                self.L.error("PUBLISH_TO_CT_EVENTS :: publishCtEvents ", "Error occured", error);
            }
            return done(error);
        });
    }

    publishMessageToKafka(ref, payload, topic, source, key) {
        let self = this;
        if (ref) {
            ref.publishData([{
                topic: topic,
                messages: JSON.stringify(payload),
                key: key
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:PUBLISH_TO_KAFKA",
                        "STATUS:ERROR",
                        "TYPE:KAFKA_PUBLISH",
                        "TOPIC:" + topic,
                        "SOURCE:" + source,
                        "OPERATOR:" + _.get(payload, ['data', 'operator'], 'NO_OPERATOR'),
                        "SERVICE:" + _.get(payload, ['data', 'service'], 'NO_SERVICE')
                    ]);
                    self.L.error('PUBLISH_TO_KAFKA :: publishToKafka', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                    throw new Error('Error while publishing message in Kafka');
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:PUBLISH_TO_KAFKA",
                        "STATUS:PUBLISHED",
                        "TYPE:KAFKA_PUBLISH",
                        "TOPIC:" + topic,
                        "SOURCE:" + source,
                        "OPERATOR:" + _.get(payload, ['data', 'operator'], _.get(payload, 'operator', 'NO_OPERATOR')),
                        "SERVICE:" + _.get(payload, ['data', 'service'], _.get(payload, ['data', 'operator'], _.get(payload, 'service', 'NO_OPERATOR')))
                    ])
                    if (_.get(payload, ['data', 'service'], _.get(payload, 'service', 'NO_OPERATOR')) != 'financial services') {
                        self.L.log('PUBLISH_TO_KAFKA :: publishToKafka', 'Message published successfully in Kafka', ' on topic ' + topic, JSON.stringify(payload));
                    }
                }
            }, [200, 800]);
        }
    }
}

export default publishToKafka