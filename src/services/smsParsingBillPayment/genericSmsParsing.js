import CATALOGVERTICALRECHARGE from '../../models/catalogVerticalRecharge';
import BILLS from '../../models/bills';
import recentBillLibrary from '../../lib/recentBills';
import utility from '../../lib';
import MOMENT from 'moment';
import <PERSON>Y<PERSON> from 'async';
import RecentsLayerLib from '../../lib/recentsLayer';
import _ from 'lodash';
import VALIDATOR from 'validator';
import OS from 'os';
import BILLS_SUBSCRIBER from '../billSubscriber';
import digitalUtility from 'digital-in-util'
import SmsParsingLagDashboard from '../../lib/smsParsingLagDashboard'
import DynamicSmsParsingRegexExecutor from './dynamicSmsParsingRegexExecutor'
import operator from 'recharge-config/operator';
import BillFetchAnalytics from '../../lib/billFetchAnalytics'
import InternalCustIdNonRUFlowTagger from '../../lib/InternalCustIdNonRUFlowTagger';
class genericSmsParsing {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.categoryId = options.categoryId;
        this.blockedPaytype = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'BLOCKED_PAYTYPES'], ['credit card']);
        this.billSubscriber = new BILLS_SUBSCRIBER(options);
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.bills = new BILLS(options);
        this.commonLib = new utility.commonLib(options);
        this.activePidLib = options.activePidLib;
        this.consentData = {};
        this.recentsLayerLib = new RecentsLayerLib(options);
        this.recentBillLibrary = new recentBillLibrary(options);
        this.operators = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.recent_bills_operators = this.recentBillLibrary._initRecentBillSpecificData(this.bills_operator_table, this.operators);
        this.infraUtils = options.INFRAUTILS;
        this.cvrReloadInterval = _.get(this.config, 'COMMON.CVR_RELOAD_INTERVAL', 86400000);
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.greyScaleEnv = options.greyScaleEnv;
        this.rechargeNumberAlreadySeen = []
        this.realtimePayloadIngestionTable = "sms_parsing_payload_ingestion"
        this.smsParsingLagDashboard = new SmsParsingLagDashboard(options);
        this.timestamps = {};
        this.RUreadsKafkaTime = new Date().getTime();       // Time at which RU reads from the KAFKA
        this.smsParsingBillsDwhRealtime = _.get(options, 'smsParsingBillsDwhRealtime', false);
        this.regexExecutor = new DynamicSmsParsingRegexExecutor(options);
        this.billFetchAnalytics = new BillFetchAnalytics(options);
        this.saveForAnalyticsInCassandraDbAndKafka = options.saveForAnalyticsInCassandraAndKafka ? true : false;
        this.internalCustIdNonRUFlowTagger = new InternalCustIdNonRUFlowTagger(options);
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    getOriginOfPayloadCurrentlyBeingProcessed(record) {
        let self = this;
        if (self.smsParsingBillsDwhRealtime) {
            return "SMS_PARSING_DWH_REALTIME"
        }
        return 'SMS_PARSING_DWH';
    }

    initializeVariable() {
        this.L.verbose("Reinitializing variables")
        this.operators = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.recent_bills_operators = this.recentBillLibrary._initRecentBillSpecificData(this.bills_operator_table, this.operators);
    }

    executeStrategy(done, record,kafkaTopic, ref) {
        let self = this;
        this.parent = ref;
        self.timestamps = {};
        self.RUreadsKafkaTime = new Date().getTime();       // Time at which RU reads from the KAFKA
        self.L.log('1. executeStrategy:: start executing on  parsed sms');

        try {
            if (!record) {
                self.L.log(`executeStrategy:: null smsData`);
                return done();
            }
            self.processRecord(record,kafkaTopic, function (err) {
                if (err) {
                    self.L.error(`GENERIC_SMS_PARSING_POSTPAID :: Error for record :${JSON.stringify(record)}, ${err}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:GENERIC_SMS_PARSING_POSTPAID',
                        `SERVICE:${_.get(record, 'category', null)}`,
                        'STATUS:PROCESSING_ERROR',
                        'SOURCE:POSTPAID_SMS',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                        `APP_VERSION:${_.get(record, 'appVersion', null)}`
                    ])
                }
                return done();
            });
        }
        catch (err) {
            self.L.error(`GENERIC_SMS_PARSING_POSTPAID :: Error for record :${JSON.stringify(record)}, ${err}`);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:GENERIC_SMS_PARSING_POSTPAID',
                `SERVICE:${_.get(record, 'category', null)}`,
                'STATUS:PROCESSING_ERROR',
                'SOURCE:POSTPAID_SMS',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                `APP_VERSION:${_.get(record, 'appVersion', null)}`
            ]);
            return done();
        }

    }


    publishInBillFetchKafka(done, processedRecord, configurations) {
        let self = this;
        let dbData = _.get(processedRecord, 'dbData', []);
        self.L.log(`10. publishInBillFetchKafka:: Record Category - ${processedRecord.service}`);

        // if(_.get(processedRecord, 'isRuSmsParsing', false)==false || _.get(processedRecord,'isDwhSmsParsing',false) == false){
        //     self.L.log("publishToBillFetchKafka:: ", 'Not publishing on REMINDER_BILL_FETCH_REALTIME kafka due to failing eligibility')
        //     return done(null);
        // }

        ASYNC.eachLimit(dbData, 3, (dataRow, cb) => {
            let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;

            if (dataRow.notification_status == 0) {
                self.L.error(`stop publishing data on REMINDER_BILL_FETCH_REALTIME via the Kafka pipeline for notification status : ${dataRow.notification_status} debugKey::`, dbDebugKey);
                return cb();
            }

            if (dataRow.is_automatic != 0 && dataRow.is_automatic != 5) {
                self.L.error(`stop publishing data on REMINDER_BILL_FETCH_REALTIME via the Kafka pipeline for automatic status : ${dataRow.is_automatic} debugKey::`, dbDebugKey);
                return cb();
            }

            let payload = {
                source: self.smsParsingBillsDwhRealtime == true ? "genericBillFetchDWHRealtime" : ((_.get(processedRecord, 'isRuSmsParsing', false) == true) ? "genericBillFetchRealtime" : "genericBillFetchDWH"),
                notificationType: "BILLGEN",
                data: {
                    customerId: dataRow.customer_id,
                    rechargeNumber: dataRow.recharge_number,
                    productId: processedRecord.productId,
                    operator: processedRecord.operator,
                    amount: processedRecord.amount,
                    bill_fetch_date: MOMENT(),
                    paytype: "postpaid",
                    service: processedRecord.service,
                    circle: processedRecord.circle,
                    customerMobile: dataRow.customer_mobile,
                    customerEmail: dataRow.customer_email,
                    status: self.config.COMMON.bills_status.BILL_FETCHED,
                    userData: null,
                    billDate: processedRecord.billDate,
                    notification_status: dataRow.notification_status,
                    dueDate: processedRecord.dueDate,
                    customerOtherInfo: JSON.stringify(processedRecord),
                    planBucket: processedRecord.planBucket,
                    is_automatic: dataRow.is_automatic
                }
            }

            let kafkaProducer;

            _.set(payload, ['data', 'billFetchReminder_onBoardTime'], new Date().getTime());

            if (_.get(processedRecord, 'isDwhSmsParsingRealtime', false) == true && (typeof self.parent.billFetchKafkaPublisher != "undefined") && self.parent.billFetchKafkaPublisher != null && configurations.KAFKA_REMINDER_BILL_FETCH_ENABLE && configurations.KAFKA_REMINDER_BILL_FETCH_ENABLE ==1) {
                kafkaProducer = self.parent.billFetchRealTimeKafkaPublisher;
                self.L.log("publishInKafka :: isDwhSmsParsingRealtime is true. Publishing in reminder kafka");
            } else if (configurations.KAFKA_REMINDER_BILL_FETCH_REALTIME_ENABLE && configurations.KAFKA_REMINDER_BILL_FETCH_REALTIME_ENABLE ==1) {
                self.L.log("publishInKafka :: isDwhSmsParsingRealtime is false. Publishing in recharges kafka");
                kafkaProducer = self.parent.billFetchKafkaPublisher;
            }
            else {
                return cb();
            }

            self.L.log("publishInKafka :: Pushing data in topic : ", (_.get(processedRecord, 'isRuSmsParsing', false) == true || _.get(processedRecord, 'isDwhSmsParsingRealtime', false) == true) ? _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', '') : _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''));
            utility.sendNotificationMetricsFromSource(payload)
            kafkaProducer.publishData([{
                topic: (_.get(processedRecord, 'isRuSmsParsing', false) == true || _.get(processedRecord, 'isDwhSmsParsingRealtime', false) == true) ? _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', '') : _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
                messages: JSON.stringify(payload)
            }], (error) => {
                if (error) {
                    utility.sendNotificationMetricsFromSource(payload, "ERROR")
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE: SMS_PARSING_POSTPAID",
                        `SERVICE:${_.get(processedRecord, 'category', null)}`,
                        'STATUS:ERROR',
                        'TYPE:KAFKA_PUBLISH',
                        'TOPIC:REMINDER_BILL_FETCH',
                        "OPERATOR:" + _.get(processedRecord, 'operator', null),
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord, 'appVersion', null)}`
                    ]);
                    self.L.critical('publishInKafka :: REMINDER_BILL_FETCH_REALTIME', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID",
                        `SERVICE:${_.get(processedRecord, 'category', null)}`,
                        'STATUS:PUBLISHED',
                        'TYPE:KAFKA_PUBLISH',
                        'TOPIC:REMINDER_BILL_FETCH',
                        "OPERATOR:" + _.get(processedRecord, 'operator', null),
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord, 'appVersion', null)}`
                    ]);
                    self.L.log('prepareKafkaResponse :: REMINDER_BILL_FETCH_REALTIME', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH', JSON.stringify(payload));
                }
                return cb(null);
            }, [200, 800]);
        }, (error, res) => {
            if (error) {
                self.L.error("postpaidSmsParsing :: publishCtEvents ", "Error occured", error);
            }
            return done(error);
        });
    }

    async processRecord(record,kafkaTopic, done) {
        let self = this;
        let category = kafkaTopic;
        self.L.log('2. processRecord:: start processing current record', JSON.stringify(record));
        let configuration=null;
        if (!self.smsParsingBillsDwhRealtime) {
            configuration = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', kafkaTopic], null);
            if (!configuration) {
                configuration = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', _.get(record, 'category', null)], null);
            }
        }
        else {
            configuration = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_REALTIME_CONFIG', kafkaTopic], null)
            if (!config) {
                configuration = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_REALTIME_CONFIG', _.get(record, 'category', null)], null);
            }
        }

        try {
            ASYNC.waterfall([
                (next) => {
                    self.validateAndProcessRecord(async (errorResponse, processedRecord) => {
                        let operator = processedRecord.operator || "NoOpertor";
                        if (errorResponse) {
                            self.L.error(`GENERIC_SMS_PARSING_POSTPAID :: VALIDATION_FAILURE`, `Invalid record received ${JSON.stringify(record)} with error ${errorResponse}`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:GENERIC_SMS_PARSING_POSTPAID',
                                `SERVICE:${_.get(record, 'category', null)}`,
                                'STATUS:ERROR',
                                'TYPE:VALIDATION_FAILURE',
                                'OPERATOR:' + operator,
                                'REASON:' + errorResponse,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record, 'appVersion', null)}`
                            ]);
                            next(errorResponse, processedRecord);
                        } else {
                            self.L.log(`GENERIC_SMS_PARSING_POSTPAID :: VALIDATION_SUCCESS`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:GENERIC_SMS_PARSING_POSTPAID',
                                `SERVICE:${_.get(record, 'category', null)}`,
                                'STATUS:SUCCESS',
                                'TYPE:VALIDATION_SUCCESS',
                                'OPERATOR:' + operator,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record, 'appVersion', null)}`
                            ]);
                            next(null, processedRecord);
                        }
                    }, record,kafkaTopic,configuration);
                },
                (processedRecord, next) => {
                    self.getForwardActionFlow((err, action) => {
                        if (err) {
                            self.L.error(`GENERIC_SMS_PARSING_POSTPAID :: getForwardActionFlow`, `invalid action found for: ${processedRecord.debugKey} with error ${err}`);
                            utility._sendMetricsToDD(1, ['REQUEST_TYPE:GENERIC_SMS_PARSING_POSTPAID', `SERVICE:${category}`, 'STATUS:ERROR', 'TYPE:NO_ACTION', `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
                            next(err, processedRecord)
                        } else {
                            let billsData = self.getBillsData(processedRecord);
                            processedRecord.billsData = billsData;
                            self.L.log(`SMS_PARSING_POSTPAID :: getForwardActionFlow`, `action: ${action}`);
                            next(null, action, processedRecord);
                        }
                    }, processedRecord)
                },
                (action, processedRecord, next) => {
                    if (action == 'findAndUpdateToCassandra' || !processedRecord.recordFoundOfSameCustId) {
                        if (processedRecord.recordFoundOfSameCustId != undefined && !processedRecord.recordFoundOfSameCustId) {
                            self.L.log(`GENERIC_SMS_PARSING_POSTPAID :: updateCassandra | Record found for same RN,but with new custId`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:GENERIC_SMS_PARSING_POSTPAID',
                                `SERVICE:${category}`,
                                'STATUS:RECORD_NOT_FOUND_OF_SAME_CID',
                                `OPERATOR:${_.get(processedRecord, 'operator', 'NO_OPERATOR')}`,
                                'TYPE:NON_PAYTM_EVENTS',
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record, 'appVersion', null)}`
                            ]);
                        }
                        self.updateCassandra((err) => {
                            if (err) {
                                self.L.error(`GENERIC_SMS_PARSING_POSTPAID :: updateCassandra`, `error while updating for : ${processedRecord.debugKey} with error: ${err}`);
                                next(err, processedRecord);
                            } else {
                                self.L.log(`GENERIC_SMS_PARSING_POSTPAID ::  updateCassandra`, `updated successfully for : ${processedRecord.debugKey}`);
                                next(null, action, processedRecord);
                            }
                        }, processedRecord,kafkaTopic,configuration);
                    } else {
                        next(null, action, processedRecord);
                    }
                },
                (action, processedRecord, next) => {
                    if (action == 'update') {
                        self.updateDbRecord((err) => {
                            if (err) {
                                self.L.error(`GENERIC_SMS_PARSING_POSTPAID :: updateDbRecord`, `error while updating for : ${processedRecord.debugKey}, error: ${err}`);
                                next(err, processedRecord);
                            } else {
                                _.set(processedRecord, 'ruOnboarded', true);
                                self.L.log(`GENERIC_SMS_PARSING_POSTPAID :: updateDbRecord`, `updated successfully for : ${processedRecord.debugKey}`);
                                next(null, processedRecord, action);
                            }
                        }, processedRecord);
                    } else {
                        self.L.log(`GENERIC_SMS_PARSING_POSTPAID :: nonPaytm user smsData || old SMS Data,  Record processed `);
                        next(null, processedRecord, action);
                    }
                },
                (processedRecord, action, next) => {
                    if (action == 'update' && _.get(processedRecord, 'is_automatic', 0) == 0) {
                        self.publishInKafka((err) => {
                            if (err) {
                                self.L.error(`GENERIC_SMS_PARSING_POSTPAID: ERROR in publishInKafka for : ${processedRecord.debugKey} with error: ${err}`);
                                utility._sendMetricsToDD(1, [
                                    'REQUEST_TYPE:GENERIC_SMS_PARSING_POSTPAID',
                                    `SERVICE:${category}`,
                                    'SOURCE:PUBLISH_KAFKA',
                                    'STATUS:ERROR',
                                    'TYPE:' + err]);
                                next(err, processedRecord);
                            } else next(null, processedRecord);
                        }, processedRecord, action,kafkaTopic,configuration);
                    } else next(null, processedRecord);
                },
                (processedRecord, next) => {
                    let service = _.get(processedRecord, 'service', null);
                    let source;
                    source = _.get(processedRecord, 'isRuSmsParsing', false) ? `SMS_${service}_POSTPAID_REALTIME` : `SMS_${service}_POSTPAID`;
                    if (self.smsParsingBillsDwhRealtime) source = `SMS_${service}_POSTPAID_DWH_RT`;
                    self.smsParsingLagDashboard.publishDelaysMetrics((err) => {
                        next(null, processedRecord);
                    }, source, self.timestamps, processedRecord.operator, processedRecord);
                },
            ], async function (error, processedRecord) {
                //     if(_.get(record, 'isRuSmsParsing', false)){
                //         await self.ingestIncomingPayloads(error, processedRecord,record)
                //     .catch((err)=>{
                //         if(err){
                //             self.L.error(`SMS_PARSING::ingestIncomingPayloads`, `couldn't save record with error ${err}`);
                //         }
                //     })
                // }
                if (error) {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:GENERIC_SMS_PARSING_POSTPAID',
                        `SERVICE:${category}`,
                        'STATUS:PROCESS_RECORD_FAILURE',
                        'SOURCE:POSTPAID_SMS',
                        'TYPE:' + error,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                        `APP_VERSION:${_.get(record, 'appVersion', null)}`
                    ]);
                    self.L.verbose(`GENERIC_SMS_PARSING_POSTPAID :: processRecords`, `Exception occured Error Msg:: ${error}`);
                } else {
                    self.L.log(`GENERIC_SMS_PARSING_POSTPAID :: processRecords`, `Record processed `);
                }
                return done();
            });
        } catch (err) {
            self.L.error('processRecord:: ', err);
            return done();
        }

    }


    validateAndProcessRecord(done, record,kafkaTopic,configuration) {
        let self = this;
        self.L.log('3. validateAndProcessRecord :: convert payload to record and validate');
        let categoryId = kafkaTopic;

        
        if (!record) {
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:GENERIC_SMS_PARSING',
                `SERVICE:${kafkaTopic}`,
                'STATUS:ERROR',
                'TYPE:RECORD_NULL',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
            return done('invalid_record', record);
        }



        let smsDateTime_fromPayload = Number(record.smsDateTime);
        const timestamp = new Date(record.timestamp).getTime(),
            smsDateTime = new Date(smsDateTime_fromPayload).getTime(),
            smsParsingEntryTime = new Date(record.producerEntryTime).getTime(),
            smsParsingExitTime = new Date(record.consumerExitTime).getTime(),
            deviceDateTime = new Date(record.deviceDateTime).getTime(),
            uploadTime = new Date(record.uploadTime).getTime(),
            collector_timestamp = new Date(record.collector_timestamp).getTime();
        _.set(self.timestamps, 'data_smsDateTime', smsDateTime);
        _.set(self.timestamps, 'data_timestamp', timestamp);
        _.set(self.timestamps, 'data_deviceDateTime', deviceDateTime);
        _.set(self.timestamps, 'data_uploadTime', uploadTime);
        _.set(self.timestamps, 'collector_timestamp', collector_timestamp);
        _.set(self.timestamps, 'RUreadsKafkaTime', self.RUreadsKafkaTime);
        _.set(self.timestamps, 'smsParsingEntryTime', smsParsingEntryTime);
        _.set(self.timestamps, 'smsParsingExitTime', smsParsingExitTime);

        if (_.get(record, 'isDwhSmsParsingRealtime', false) && (typeof _.get(record, 'smsDateTime', null) == "string") && _.get(record, 'smsDateTime', null) != null) {
            _.set(record, 'smsDateTime', parseInt(_.get(record, 'smsDateTime', null)));
        }

        if (_.get(record, 'smsDateTime', null)) {
            if (record.smsDateTime.toString().length === 10) {
                record.smsDateTime = record.smsDateTime * 1000;
            }
        } else {
            _.set(record, 'smsDateTime', MOMENT().format('YYYY-MM-DD'));
        }


        let customerId = typeof _.get(record, 'cid') === 'number'
            ? _.get(record, 'cId')
            : (typeof _.get(record, 'cId') === 'string' && VALIDATOR.isNumeric(_.get(record, 'cId')))
                ? VALIDATOR.toInt(_.get(record, 'cId'))
                : null,

        amount = _.get(record, 'amount', null) ? utility.getFilteredAmount(_.get(record, 'amount', null)) : null,
        dueDate = utility.getFilteredDate( _.get(record, 'dueDate', null)).value,
        billDate = utility.getFilteredDate( _.get(record, 'billDate',null)).value || MOMENT(record.smsDateTime),

        operator = _.toLower(_.get(self.config, ['DYNAMIC_CONFIG', `DWH_${kafkaTopic}_SMS_PARSING_CONFIG`, 'GENERIC_DWH_OPERATOR_MAPPING', operator], _.get(record, 'operator', null)));
        utility._sendMetricsToDD(1, [
            'REQUEST_TYPE:GENERIC_SMS_PARSING',
            `SERVICE:${kafkaTopic}`,
            'STATUS:TRAFFIC',
            'TYPE:OPERATOR_AFTER_OPERATOR_MAPPING',
            'OPERATOR:' + operator,
            `ORIGIN:${_.get(record, 'isDwhSmsParsingRealtime', false) == true ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH"}`
        ]);


        if (configuration.rechargeNumber_regex) {
            const regex = new RegExp(configuration.rechargeNumber_regex);

            //going inside even config not defined
            if (!regex.test(record.rechargeNumber)) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:GENERIC_SMS_PARSING',
                    `SERVICE:${kafkaTopic}`,
                    'STATUS:ERROR',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                ]);
                if (self.saveForAnalyticsInCassandraAndKafka(record)) {
                    return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(
                        self.createRecordForAnalytics(record, null, null),
                        `rechargeNumber Invalid: ${config.errorCodes.invalidFormat}`,
                        done,
                        record
                    );
                }
                return done('rechargeNumber Invalid', record);
            }


        }



        let processedRecord = {
            "operator": operator,
            "customerId": customerId,
            "rechargeNumber":  _.get(record, 'rechargeNumber', null),
            "gateway": null,
            "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            "billDate": billDate ? billDate.endOf('day').format('YYYY-MM-DD') : null,
            "dueDate": dueDate ? dueDate.endOf('day').format('YYYY-MM-DD HH:mm:ss') : null,
            "amount": amount,
            "status": _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4),
            "paytype": 'postpaid',
            "cache": null,
            "service_id": _.get(self.recent_bills_operators, [operator, 'serviceId'], 0),
            "customerMobile": _.get(record, 'smsReceiver', null),
            "customerEmail": _.get(record, 'smsReceiverEmail', null),
            "extra": null,
            "customer_type": null,
            "paymentDate": null,
            "user_data": null,
            "msgId": _.get(record, 'msg_id', ''),
            "rtspClassId": _.get(record, 'rtspClassId', null),
            "dwh_classId": _.get(record, 'level_2_category', null),
            "rtspClassName": _.get(record, 'rtspClassName', null),
            "sender_id": _.get(record, 'smsSenderID', null),
            "sms_id": _.get(record, 'msg_id', null),
            "sms_date_time": _.get(record, 'smsDateTime', null),
            "category": _.get(record, 'category', null),
            "appVersion": _.get(record, 'appVersion', null),
            "smsDateTime": _.get(record, 'smsDateTime', null)
        };

        if (record.dueDate == null || record.amount == null) {
            processedRecord.partialRecordFound = true;
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:GENERIC_SMS_PARSING',
                `SERVICE:${kafkaTopic}`,
                'STATUS:ERROR',
                'TYPE:PARTIAL_BILL_FOUND',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
        }

        _.get(record, 'isDwhSmsParsing', false) ?
            _.set(processedRecord, 'isDwhSmsParsing', true) : _.get(record, 'isDwhSmsParsingRealtime', false) && _.set(processedRecord, 'isDwhSmsParsingRealtime', true);



        if (operator) {
            _.set(processedRecord, 'productId', _.get(self.config, ['DYNAMIC_CONFIG', `GENERIC_${kafkaTopic}_SMS_PARSING`, operator, 'PRODUCT_ID'], null));
        }

        self.L.log('3. validateAndProcessRecord :: payload after processing', processedRecord);

        let rechargeNumber2 = _.toLower(_.get(record, 'rechargeNumber2', null));
        let pidMapKey = (operator + (rechargeNumber2 != null && rechargeNumber2 != '' ? `_${rechargeNumber2}` : '')).replace(/ /g, '_');
        self.setPidAttributes(processedRecord,record, pidMapKey, "GENERIC_SMS_PARSING",kafkaTopic);


        self.checkCommonValidationForRecord(processedRecord,record, configuration,kafkaTopic).
            then((processedRecord) => {
                self.checkValidationForDueDateAndAmount(processedRecord,record,kafkaTopic)
                    .then((processedRecord) => {
                        let activePid = self.activePidLib.getActivePID(processedRecord.productId);
                        self.L.verbose('processRecord', `Found active Pid ${activePid} against PID ${processedRecord.productId}`);
                        processedRecord.oldProductId = processedRecord.productId; // Keeping track of original PID
                        processedRecord.productId = activePid;    // Replacing active PID
                      
                        let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', processedRecord.productId], null) || _.get(self.config, ['OPERATOR_TABLE_REGISTRY', processedRecord.operator], null);

                        if (!tableName) {
                            self.L.error(`processRecord:: ${processedRecord.operator} not migrated`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:GENERIC_SMS_PARSING',
                                `SERVICE:${kafkaTopic}`,
                                "STATUS:ERROR",
                                'TYPE:TABLE_NOT_FOUND',
                                'OPERATOR:' + processedRecord.operator,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record, 'appVersion', null)}`
                            ]);
                            if (self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate == null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", null), `Table not found for given operator`, done, processedRecord);
                            return done(`Table not found for ${processedRecord.operator}`, processedRecord);
                        }
                        self.L.log(`processRecord:: table_name found for operator: ${processedRecord.operator}:${tableName}`);
                        _.set(processedRecord, 'tableName', tableName);

                        let nextBillFetchDate = self.getNextBillFetchDate(processedRecord);
                        self.L.log(`SMS_PARSING_POSTPAID: getNextBillFetchDate for sms Data, NBFD: ${nextBillFetchDate}`);
                        _.set(processedRecord, 'nextBillFetchDate', nextBillFetchDate);

                        return done(null, processedRecord);

                    }).catch((error) => {
                        done(error,processedRecord);
                    });
            })
            .catch((error) => {
                done(error,processedRecord);
            });

    }

    checkValidationForDueDateAndAmount(processedRecord, record,kafkaTopic) {
        let self = this;
        let minAmount = _.get(self.config, ['DYNAMIC_CONFIG', `GENERIC_${kafkaTopic}_SMS_PARSING`, 'COMMON', 'MIN_AMOUNT'], 0);
        let maxAmount = _.get(self.config, ['DYNAMIC_CONFIG', `GENERIC_${kafkaTopic}_SMS_PARSING`, 'COMMON', 'MAX_AMOUNT'], 10000);
    
        return new Promise((resolve, reject) => {
            if (processedRecord.amount != null && processedRecord.amount < minAmount) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:GENERIC_SMS_PARSING',
                    `SERVICE:${kafkaTopic}`,
                    'STATUS:ERROR',
                    'OPERATOR:' + processedRecord.operator,
                    'TYPE:AMOUNT_LESS_THAN_MIN_AMOUNT',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                ]);
    
                if (self.saveForAnalyticsInCassandraAndKafka(processedRecord)) {
                    self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(
                        self.createRecordForAnalytics1(
                            processedRecord,
                            (processedRecord.dueDate == null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL",
                            null
                        ),
                        'Amount less than minAmount',
                        null,
                    ).then(() => reject('Amount less than minAmount' ));
                } else {
                    reject('Amount less than minAmount' );
                }
            } else if (processedRecord.amount != null && processedRecord.amount > maxAmount) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:GENERIC_SMS_PARSING',
                    `SERVICE:${kafkaTopic}`,
                    'STATUS:ERROR',
                    'OPERATOR:' + processedRecord.operator,
                    'TYPE:AMOUNT_MORE_THAN_MAX_AMOUNT',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                ]);
    
                if (self.saveForAnalyticsInCassandraAndKafka(processedRecord)) {
                    self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(
                        self.createRecordForAnalytics1(
                            processedRecord,
                            (processedRecord.dueDate == null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL",
                            null
                        ),
                        'Amount greater than maxAmount',
                        null,
                    ).then(() => reject('Amount greater than maxAmount'));
                } else {
                    reject('Amount greater than maxAmount' );
                }
            } else if (processedRecord.dueDate && (!MOMENT(processedRecord.dueDate).isValid() || MOMENT(processedRecord.dueDate).diff(MOMENT().endOf('day'), 'days') < 0)) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:GENERIC_SMS_PARSING',
                    `SERVICE:${kafkaTopic}`,
                    'STATUS:ERROR',
                    'OPERATOR:' + processedRecord.operator,
                    'TYPE:DUE_DATE_INVALID_OR_IN_PAST',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                ]);
    
                if (self.saveForAnalyticsInCassandraAndKafka(processedRecord)) {
                    self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(
                        self.createRecordForAnalytics1(
                            processedRecord,
                            (processedRecord.dueDate == null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL",
                            null
                        ),
                        'DueDate is invalid || DueDate in past',
                        null
                    ).then(() => reject('DueDate is invalid || DueDate in past' ));
                } else {
                    reject('DueDate is invalid || DueDate in past' );
                }
            } else {
                resolve(processedRecord);
            }
        });
    }
    



    checkCommonValidationForRecord(processedRecord,record, config,kafkaTopic) {
        let self = this;
        return new Promise(async (resolve, reject) => {
            let isValidRechargeNumber = this.regexExecutor.checkValidityOfRechargeNumberByRegex(processedRecord);


            if (!isValidRechargeNumber) {
                self.L.log("checkCommonValidationForRecord :: rechargeNumber Invalid (Regex) for record ", JSON.stringify(processedRecord));
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:GENERIC_SMS_PARSING', `SERVICE:${kafkaTopic}`, 'STATUS:FAILURE', 'TYPE:RN_REGEX_VALIDATION', `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
                if (self.saveForAnalyticsInCassandraAndKafka(processedRecord)) {
                    await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(
                        self.createRecordForAnalytics1(
                            processedRecord,
                            (processedRecord.dueDate == null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL",
                            null
                        ),
                        "rechargeNumber Invalid (Regex)"
                    ).then(() => {
                        return reject("rechargeNumber Invalid (Regex)");
                    }).catch((err) => {
                        self.L.error("checkCommonValidationForRecord :: error in saving and publishing bill fetch analytics data", err);
                        return reject(err);
                    });
                } else {
                    return reject('rechargeNumber Invalid (Regex)');
                }
            } else {
                let debugKey = `rech_num:${processedRecord.rechargeNumber}::operator:${processedRecord.operator}::service:${processedRecord.service}::productId:${processedRecord.productId}`;
                _.set(processedRecord, 'debugKey', debugKey);

                self.L.log("checkCommonValidationForRecord :: rechargeNumber valid (Regex) for record ", processedRecord.debugKey);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:GENERIC_SMS_PARSING', `SERVICE:${kafkaTopic}`, 'STATUS:SUCCESS', 'TYPE:RN_REGEX_VALIDATION', `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);


                const requiredFields = _.get(config, 'requiredFields', ['rechargeNumber', 'operator']);
                const missingFields = requiredFields.filter(field => !_.get(record, field, null));

                if (missingFields.length > 0) {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:GENERIC_SMS_PARSING',
                        `SERVICE:${kafkaTopic}`,
                        'STATUS:ERROR',
                        'TYPE:MANDATORY_PARAMS_NOT_PRESENT',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                    ]);


                    if (self.saveForAnalyticsInCassandraAndKafka(record)) {
                        self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(
                            self.createRecordForAnalytics(record, null, null),
                            `Mandatory Params ${missingFields} is Missing / Invalid`
                        ).then(() => reject(`Mandatory Params ${missingFields} is Missing / Invalid`))
                    } else {
                        return reject(`Mandatory Params ${missingFields} is Missing / Invalid`, record);
                    }
                }
                resolve(processedRecord);
            }
        });
    }

    setPidAttributes(processedRecord,record, pidMapKey, metricType,kafkaTopic) {
        let self=this;

        if (!_.get(processedRecord, 'productId', null)) {

            
                self.L.log('3. validateAndProcessRecord :: pidMapKey', pidMapKey);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:${metricType}`,
                    `SERVICE:${kafkaTopic}`,
                    'TYPE:PID_MAP_KEY',
                    'PRODUCT_ID:' + pidMapKey,
                    `ORIGIN:${_.get(record, 'isDwhSmsParsingRealtime', false) == true ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH"}`
                ]);
                _.set(processedRecord, 'productId', _.get(self.config, ['DYNAMIC_CONFIG', `DWH_${kafkaTopic}_SMS_PARSING_CONFIG`, 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', pidMapKey], _.get(self.config, ['DYNAMIC_CONFIG', `DWH_${kafkaTopic}_SMS_PARSING_CONFIG`, 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', operator], null)));
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:${metricType}`,
                    `SERVICE:${kafkaTopic}`,
                    'TYPE:OPERATOR_AFTER_PRODUCT_ID',
                    `PRODUCT_ID: ${_.get(processedRecord, 'productId', 'NO_PRODUCT_ID')}`,
                    `ORIGIN:${_.get(record, 'isDwhSmsParsingRealtime', false) == true ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH"}`
                ]);
            }
            
        

        if (_.get(processedRecord, 'productId', null)) {
            try {
                _.set(processedRecord, 'paytype', _.toLower(_.get(this.config, ['CVR_DATA', processedRecord.productId, 'paytype'])), null),
                    _.set(processedRecord, 'service', _.toLower(_.get(this.config, ['CVR_DATA', processedRecord.productId, 'service'])), null);
            } catch (err) {
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:${metricType}`,
                    `SERVICE:${kafkaTopic}`,
                    'STATUS:ERROR',
                    'OPERATOR:' + processedRecord.operator,
                    'TYPE:SETTING_PAYTYPE_SERVICE'
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                ]);
                self.L.error("Couldn't set paytype and service from cvr for record ", JSON.stringify(processedRecord))
            }
        }
    }


    async getForwardActionFlow(done, processedRecord) {
        let self = this;
        self.L.log('4. getForwardActionFlow:: starting getForwardActionFlow');
        self.getRecordsFromDb((err, recordsFound) => {
            if (err) {
                self.L.error(`GENERIC_SMS_PARSING_POSTPAID: ERROR in getRecordsFromDb with error: ${err} for ${processedRecord.debugKey}`);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:GENERIC_SMS_PARSING',
                    `SERVICE:${_.get(processedRecord, 'category', null)}`,
                    'STATUS:ERROR',
                    'TYPE:ERROR_GETTING_RECORD_FROM_DB',
                    `OPERATOR:${_.get(processedRecord, 'operator', 'NO_OPERATOR')}`,
                    'SOURCE:getForwardActionFlow',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`
                ]);
                if (self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate == null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", null), `ERROR in getRecordsFromDb with error: ${err}`, done);
                return done(err);
            } else {
                if (recordsFound) {
                    self.L.log(`processRecord:: ${processedRecord.noOfFoundRecord} recordsFound :`, `for the processedRecord: ${processedRecord.debugKey}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:GENERIC_SMS_PARSING',
                        `SERVICE:${_.get(processedRecord, 'category', null)}`,
                        'STATUS:SUCCESS',
                        `OPERATOR:${_.get(processedRecord, 'operator', 'NO_OPERATOR')}`,
                        'TYPE:RECORD_FOUND_IN_DB',
                        'SOURCE:POSTPAID_SMS',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord, 'appVersion', null)}`
                    ]);

                    if (_.isNull(processedRecord.dueDate) || _.isNull(processedRecord.amount)) {
                        self.L.log(`processRecord:: DueDate or Amount found as null, so skipping this record ${processedRecord.debugKey}`);
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:GENERIC_SMS_PARSING',
                            `SERVICE:${_.get(processedRecord, 'category', null)}`,
                            'STATUS:ERROR',
                            `OPERATOR:${_.get(processedRecord, 'operator', 'NO_OPERATOR')}`,
                            'TYPE:DUE_DATE_AMOUNT_NULL',
                            'SOURCE:getForwardActionFlow',
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`
                        ]);
                        if (self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate == null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "RU"), `DueDate or Amount found as null`, done);
                        return done('DueDate or Amount found as null');
                    }

                    if (_.get(recordsFound, 'recordFoundOfSameCustId', null) == false) {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:GENERIC_SMS_PARSING',
                            `SERVICE:${_.get(processedRecord, 'category', null)}`,
                            'STATUS:ERROR',
                            `OPERATOR:${_.get(processedRecord, 'operator', 'NO_OPERATOR')}`,
                            'TYPE:RECORD_FOUND_OF_DIFFERENT_CUSTOMER_ID',
                            'SOURCE:getForwardActionFlow',
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`
                        ]);
                    }

                    let dbRecord = _.get(processedRecord, 'dbData', {}),
                        dbDueDate = _.get(dbRecord, '[0].due_date', null),
                        dueDateDiff = MOMENT(processedRecord.dueDate).startOf('day').diff(MOMENT(dbDueDate).utc().startOf('day')),
                        dbStatus = _.get(dbRecord, '[0].status', null);

                    if (MOMENT(dbDueDate).isValid() && (!dueDateDiff || dueDateDiff < 1)) {
                        self.L.log(`GENERIC_SMS_PARSING_POSTPAID: getRecordsFromDb , smsDueDate:${MOMENT(processedRecord.dueDate).startOf('day').format('YYYY-MM-DD')} <= dbDueDate:${MOMENT(dbDueDate).utc().startOf('day').format('YYYY-MM-DD')}`);
                        processedRecord.dueDate = dbDueDate;
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:GENERIC_SMS_PARSING",
                            `SERVICE:${_.get(processedRecord, 'category', null)}`,
                            "STATUS:ERROR",
                            `OPERATOR:${_.get(processedRecord, 'operator', 'NO_OPERATOR')}`,
                            'TYPE:DUE_DATE_IN_DB_GREATER',
                            'SOURCE:POSTPAID_SMS',
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                            `APP_VERSION:${_.get(processedRecord, 'appVersion', null)}`
                        ]);
                        if (self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate == null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "RU"), 'smsDueDate less than dbDueDate', done);
                        return done('smsDueDate less than dbDueDate');
                    }
                    else if (dbStatus == _.get(self.config, ['COMMON', 'bills_status', 'DISABLED'], 7) || dbStatus == _.get(self.config, ['COMMON', 'bills_status', 'NOT_IN_USE'], 13)) {
                        self.L.log(`GENERIC_SMS_PARSING_POSTPAID: getRecordsFromDb , dbStatus: ${dbStatus}`);
                        processedRecord.dueDate = dbDueDate;
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:GENERIC_SMS_PARSING",
                            `SERVICE:${_.get(processedRecord, 'category', null)}`,
                            "STATUS:ERROR",
                            `OPERATOR:${_.get(processedRecord, 'operator', 'NO_OPERATOR')}`,
                            'TYPE:INACTIVE_RECORD',
                            'SOURCE:POSTPAID_SMS',
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                            `APP_VERSION:${_.get(processedRecord, 'appVersion', null)}`
                        ]);
                        if (self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate == null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "RU"), 'inactive record in db', done);
                        return done('inactive record in db');
                    }
                    else {
                        return done(null, 'update', processedRecord);
                    }
                } else {
                    self.L.log(`processRecord:: No recordsFound in DB for the processedRecord: ${processedRecord.debugKey}`);
                    return done(null, 'findAndUpdateToCassandra', processedRecord);
                }
            }
        }, processedRecord);

    }

    async updateBillsInRecent(done, record) {
        let self = this;
        self.L.log(`8. updateBillsInRecent`, `Going to update in recent for ${record.debugKey}`);
        let queryParam = {
            recharge_number: _.get(record, 'rechargeNumber', null),
            operator: _.get(record, 'operator', null),
            paytype: _.get(record, 'paytype', null),
            service: _.get(record, 'service', null)
        }, fieldValue = {
            due_date: record.dueDate,
            bill_date: record.billDate,
            amount: _.get(record, 'amount', null),
            original_due_amount: _.get(record, 'amount', null),
            label: _.get(record, 'amount', null) && _.get(record, 'dueDate', null) ? `Bill Payment of Rs${_.get(record, 'amount', null)} due on ${MOMENT(record.dueDate).format('DD MMM YYYY')}` : null
        };
        self.recentsLayerLib.update(function (error) {
            self.L.log('updateBillsInRecent::recentsLayerLib.update', `update recents request completed for ${record.debugKey},error if any is:${error}`);
            return done(error);
        }, queryParam, "bills", [fieldValue], "smsParsingPostpaid");
    }

    async updateDbRecord(done, record) {
        let self = this;
        let billsData = record.billsData;
        self.L.log('6. updateDbRecord:: starting updateDbRecord');
        if (record.activeRecordsInDB == 0) {
            self.L.log('updateRecord', `No actve records in DB, so skipping update for ${record.debugKey}`);
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:GENERIC_SMS_PARSING",
                `SERVICE:${_.get(record, 'category', null)}`,
                'STATUS:ERROR',
                'TYPE:NO_ACTIVE_RECORDS',
                "OPERATOR:" + record.operator,
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                `APP_VERSION:${_.get(record, 'appVersion', null)}`
            ]);
            if (self.saveForAnalyticsInCassandraAndKafka(record)) return self.saveAndPublishBillFetchAnalyticsDataWithoutError(self.createRecordForAnalytics1(record, (record.dueDate == null || record.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "RU"), 'No actve records in DB, so skipping update', done);
            return done(null);
        } else if (_.get(record, 'is_automatic', 0) != 0) {
            self.L.log('updateDbRecord::', `automatic record, updating NBFD for ${record.debugKey}`);
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:GENERIC_SMS_PARSING",
                `SERVICE:${_.get(record, 'category', null)}`,
                'STATUS:TRAFFIC',
                'TYPE:UPDATE_BILLS_NBFD',
                "OPERATOR:" + record.operator,
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                `APP_VERSION:${_.get(record, 'appVersion', null)}`
            ]);
            self.bills.updateBillsNBFD(done, record.tableName, record);
        } else {
            _.set(billsData, 'is_automatic', null);
            self.L.log('updateRecord', `Updating records in sql DB for ${record.debugKey}`);
            self.bills.updateBillForSameRechargeNumPostpaid((err) => {
                _.set(self.timestamps, 'RUupdatesDbTime', new Date().getTime());
                if (err) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:GENERIC_SMS_PARSING",
                        `SERVICE:${_.get(record, 'category', null)}`,
                        'STATUS:ERROR',
                        "TYPE:UPDATE_SQL",
                        "OPERATOR:" + record.operator,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                        `APP_VERSION:${_.get(record, 'appVersion', null)}`
                    ]);
                    if (self.saveForAnalyticsInCassandraAndKafka(record)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(record, (record.dueDate == null || record.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "RU"), err, done);
                    return done(err);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:GENERIC_SMS_PARSING",
                        `SERVICE:${_.get(record, 'category', null)}`,
                        'STATUS:SUCCESS',
                        'TYPE:UPDATE_SQL',
                        "OPERATOR:" + record.operator,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                        `APP_VERSION:${_.get(record, 'appVersion', null)}`
                    ]);
                    self.bills.updateBillSource(function () {
                        // self.updateBillsInRecent((err)=>{
                        _.set(self.timestamps, 'RUupdateRecentTime', new Date().getTime());
                        // if(err){
                        //     utility._sendMetricsToDD(1, [
                        //         "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                        //         `SERVICE:${_.get(record, 'category', null)}`, 
                        //         'STATUS:ERROR', 
                        //         'TYPE:UPDATE_RECENT', 
                        //         "OPERATOR:" + record.operator,
                        //         `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                        //         `APP_VERSION:${_.get(record,'appVersion', null)}`
                        //     ]);
                        //     return done(null)
                        // }else{
                        // utility._sendMetricsToDD(1, [
                        //     "REQUEST_TYPE:SMS_PARSING_POSTPAID", 
                        //     `SERVICE:${_.get(record, 'category', null)}`, 
                        //     'STATUS:SUCCESS', 
                        //     'TYPE:UPDATE_RECENT', 
                        //     "OPERATOR:" + record.operator,
                        //     `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                        //     `APP_VERSION:${_.get(record,'appVersion', null)}`
                        // ]);
                        if (record.is_automatic == 3 || record.is_automatic == 4) {          //automatic failed case
                            self.L.log('updateRecord::', `resetIsAutomatic with tableName: ` + record.tableName + `, rechargeNumber: ` + record.rechargeNumber);
                            self.bills.resetIsAutomatic((err) => {
                                _.set(self.timestamps, 'RUupdateRecentTime', new Date().getTime());
                                if (err) {
                                    utility._sendMetricsToDD(1, [
                                        "REQUEST_TYPE:GENERIC_SMS_PARSING",
                                        `SERVICE:${_.get(record, 'category', null)}`,
                                        'STATUS:ERROR',
                                        'TYPE:UPDATE_SQL',
                                        "OPERATOR:" + record.operator,
                                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                        `APP_VERSION:${_.get(record, 'appVersion', null)}`
                                    ]);
                                    return done(err);
                                } else {
                                    utility._sendMetricsToDD(1, [
                                        "REQUEST_TYPE:GENERIC_SMS_PARSING",
                                        `SERVICE:${_.get(record, 'category', null)}`,
                                        'STATUS:SUCCESS',
                                        'TYPE:UPDATE_SQL',
                                        "OPERATOR:" + record.operator,
                                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                        `APP_VERSION:${_.get(record, 'appVersion', null)}`
                                    ]);
                                    return done(null);
                                }
                            }, record.tableName, record.rechargeNumber, [3, 4]);
                            self.L.log('resetIsAutomatic::', "reseting is_autoamtic complete in smsParsing");
                        } else {
                            self.L.log('updateRecord::', `resetIsAutomatic is not required`);
                            return done(null);
                        }
                        //  }
                        // },record);
                    }, record.tableName, 'sms', self.getOriginOfPayloadCurrentlyBeingProcessed(record), record.customerId, record.rechargeNumber);
                }
            }, record.tableName, billsData);
        }
    }

    async updateCassandra(done, processedRecord,kafkaTopic,configurations) {
        let self = this;
        self.L.log('5. updateCassandra:: starting updateCassandra');


        try {
            let extra = {};
            extra.eventState = "bill_gen";
            extra.billSource = "sms_parsed";
            extra.updated_data_source = self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord);
            extra.created_source = self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord);
            extra.demergerOperatorsList = _.get(processedRecord, 'demergerOperatorsList', null);

            if (_.get(processedRecord, 'isRuSmsParsing', false)) {
                extra.isRuSmsParsing = true;
            }
            if (_.get(processedRecord, 'isDwhSmsParsing', false)) {
                extra.isDwhSmsParsing = true;
            } else if (_.get(processedRecord, 'isDwhSmsParsingRealtime', false)) {
                extra.isDwhSmsParsingRealtime = true;
            }

            let dataToBeInsertedInDB = {
                customerId: processedRecord.customerId,
                rechargeNumber: processedRecord.rechargeNumber,
                productId: processedRecord.productId,
                operator: processedRecord.operator,
                amount: processedRecord.amount,
                dueDate: MOMENT(processedRecord.dueDate).isValid() ? MOMENT(processedRecord.dueDate).format('YYYY-MM-DD HH:mm:ss') : null,
                billDate: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                billFetchDate: MOMENT(processedRecord.billFetchDate).format('YYYY-MM-DD HH:mm:ss'),
                paytype: processedRecord.paytype,
                service: processedRecord.service,
                circle: processedRecord.circle,
                categoryId: _.get(self.config, ['CVR_DATA', processedRecord.productId, 'category_id'], null),
                customer_mobile: null,
                customer_email: null,
                status: _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4),
                notificationStatus: _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),
                customerOtherInfo: _.get(processedRecord, ['billsData', 'customerOtherInfo'], '{}'),
                extra: JSON.stringify(extra),
                dbEvent: "upsert",
                dwhClassId: _.get(processedRecord, 'dwhClassId', null),
                rtspClassId: _.get(processedRecord, 'rtspClassId', null),
                source: self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord),
                demergerOperatorsList: _.get(processedRecord, 'demergerOperatorsList', null)
            }

            
            if (configurations.KAFKA_NON_PAYTM_ENABLE && configurations.KAFKA_NON_PAYTM_ENABLE == 1) {
                return self.pushToNonPaytm(done, dataToBeInsertedInDB, processedRecord, "GENERIC_SMS_PARSING");
            }
            done(null);
            

        } catch (error) {
            self.L.error(" updateCassandra error:", error);
            if (self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate == null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "NON_RU"), `updateCassandra error: ${error}`, done);
            done(error);
        }
    }

    async pushToNonPaytm(done, dataToBeInsertedInDB, processedRecord, metricType,ref) {
        let self = this;
        let reference=null;
        !ref ? reference = self.parent : reference = ref;
        let nonRuDataToPublish = await self.internalCustIdNonRUFlowTagger.mapInternalCustIdToNonRUFlow(dataToBeInsertedInDB);
        reference.nonPaytmKafkaPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', ''),
            messages: nonRuDataToPublish
        }], (error) => {
            if (error) {
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:${metricType}`,
                    `SERVICE:${_.get(processedRecord, 'category', null)}`,
                    'STATUS:ERROR',
                    `OPERATOR:${_.get(processedRecord, 'operator', 'NO_OPERATOR')}`,
                    "TYPE:NON_PAYTM_EVENTS",
                    "TOPIC:NON_PAYTM_RECORDS_DWH",
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                    `APP_VERSION:${_.get(processedRecord, 'appVersion', null)}`
                ]);
                self.L.critical('GENERIC_SMS_PARSING :: nonPaytmKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + nonRuDataToPublish, error);
                if (self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate == null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "NON_RU"), 'Error while publishing message in Kafka', done);
                return done('Error while publishing message in Kafka');
            } else {
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:${metricType}`,
                    `SERVICE:${_.get(processedRecord, 'category', null)}`,
                    'STATUS:PUBLISHED',
                    "TYPE:NON_PAYTM_EVENTS",
                    "TOPIC:NON_PAYTM_RECORDS_DWH",
                    "OPERATOR:" + dataToBeInsertedInDB.operator,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                    `APP_VERSION:${_.get(processedRecord, 'appVersion', null)}`
                ]);
                self.L.log('SMS_PARSING_POSTPAID :: nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS', nonRuDataToPublish);
                return done(null);
            }
        })
    }

    async getRecordsFromDb(done, record) {
        let self = this;
        if (_.get(record, 'demergerOperatorsList', null) != null && _.isArray(record.demergerOperatorsList)) {
            let oldOperator = record.operator;
            self.iterateOnDbs(record, (err, foundInDb) => {
                if (err) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:GENERIC_SMS_PARSING_ITERATE_DB",
                        'STATUS:ERROR',
                        "OPERATOR:" + record.operator
                    ]);
                    _.set(record, 'operator', oldOperator);
                    self.L.log('getRecordsFromDb :: not found in all DBs');
                    return done(err, false);
                } else if (foundInDb == 0) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:GENERIC_SMS_PARSING_ITERATE_DB",
                        'STATUS:FAILURE',
                        "OPERATOR:" + record.operator,
                        "REASON:RECORD_NOT_FOUND"
                    ]);
                    _.set(record, 'operator', oldOperator);
                    self.L.log('getRecordsFromDb :: not found in all DBs');
                    return done(null, false);
                } else if (foundInDb == 1) {
                    self.L.log('getRecordsFromDb :: found in one of DBs');
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:GENERIC_SMS_PARSING_ITERATE_DB",
                        'STATUS:SUCCESS',
                        "OPERATOR:" + record.operator,
                        "REASON:RECORD_FOUND"
                    ]);
                    return done(null, true);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:GENERIC_SMS_PARSING_ITERATE_DB",
                        'STATUS:FAILURE',
                        "OPERATOR:" + record.operator,
                        "REASON:RECORD_FOUND_IN_MULTIPLE_DB",
                    ]);
                    _.set(record, 'operator', oldOperator);
                    self.L.log(`getRecordsFromDb :: found in multiple DBs::${foundInDb}`);
                    return done("found in multiple DBs");
                }
            });
        } else {
            self.L.log('getRecordsFromDb :: not array operator');
            self.bills.getBillsOfSameRech((err, data) => {
                if (err) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:GENERIC_SMS_PARSING_GETRECORDS",
                        'STATUS:ERROR',
                        "OPERATOR:" + record.operator
                    ]);
                    return done(err, false);
                }
                if (!data || !_.isArray(data) || data.length < 1) return done(null, false);

                let recordOfSameCustId = data.filter((dataValue) => dataValue.customer_id == record.customerId).length > 0 ? true : false;

                _.set(record, 'noOfFoundRecord', data.length);
                _.set(record, 'is_automatic', data[0].is_automatic);
                _.set(record, 'dbData', self.getSortedDbData(data));
                _.set(record, 'recordFoundOfSameCustId', recordOfSameCustId);
                _.set(record, 'activeRecordsInDB', self.getActiveRecords(data));
                _.set(record, 'isRecordExist', true);

                return done(null, true);
            }, record.tableName, record);
        }
    }

    iterateOnDbs(record, cb) {
        let self = this;
        let recordFoundInDb = 0;
        ASYNC.eachSeries(record.demergerOperatorsList, (subOperator, next) => {
            // in case table config is disabled, set main operator as tablename
            let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', subOperator], null);
            _.set(record, 'operator', subOperator);
            if (tableName != null) {
                try {
                    self.bills.getBillsOfSameRech((err, data) => {
                        if (err) {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:GENERIC_SMS_PARSING_GETRECORDS",
                                'STATUS:ERROR',
                                "OPERATOR:" + record.operator,
                                "REASON:QUERY_ERROR_FROM_ITERATION_ON_TABLE",
                                "TABLE_NAME:" + tableName
                            ]);
                            return next(err);
                        }
                        else {
                            if (!data || !Array.isArray(data) || data.length < 1) {
                                self.L.error('iterateOnDbs :: not found in one DB named', tableName);
                                return next();
                            } else {
                                recordFoundInDb += 1;
                                self.L.log('iterateOnDbs :: found in one DB named {}', tableName);
                                let recordOfSameCustId = data.filter((dataValue) => dataValue.customer_id == record.customerId).length > 0 ? true : false;
                                _.set(record, 'noOfFoundRecord', data.length);
                                _.set(record, 'is_automatic', data[0].is_automatic);
                                _.set(record, 'dbData', self.getSortedDbData(data));
                                _.set(record, 'recordFoundOfSameCustId', recordOfSameCustId);
                                _.set(record, 'activeRecordsInDB', self.getActiveRecords(data));
                                _.set(record, 'operator', subOperator);
                                _.set(record, 'tableName', tableName);
                                _.set(record, 'isRecordExist', true);
                                return next();
                            }

                        }
                    }, tableName, record);
                } catch (error) {
                    // Handle errors
                    return next();
                }
            } else {
                self.L.error(`iterateOnDbs:: ${record.operator} not migrated`);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:GENERIC_SMS_PARSING',
                    `SERVICE: ${record.category}`,
                    "STATUS:ERROR",
                    'TYPE:TABLE_NOT_FOUND',
                    'OPERATOR:' + record.operator,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                    `APP_VERSION: ${_.get(record, 'appVersion', null)}`
                ]);
                return next();
            }
        }, function (err) {
            if (err) {
                self.L.error('iterateOnDbs:: Error in getting data from db error', err);
                return cb(err);
            } else {
                self.L.log('iterateOnDbs:: recordFoundInDb::', recordFoundInDb);
                return cb(null, recordFoundInDb);
            }
        });
    }

    getBillsData(record) {
        let self = this;
        let dbRecord,
            dbExtra = {},
            dbCustomerOtherInfo = {};
        if (record.isRecordExist) {
            try {
                dbRecord = _.get(record, 'dbData[0]', {});
                dbExtra = JSON.parse(_.get(dbRecord, 'extra', {}));
                if (!dbExtra) dbExtra = {};
                dbCustomerOtherInfo = JSON.parse(_.get(dbRecord, 'customerOtherInfo', {}));
                if (!dbCustomerOtherInfo) dbCustomerOtherInfo = {};
            } catch (err) {
                self.L.error("getBillsData", "Error in JSON parsing" + err);
            }
        }
        let custInfoValues = dbCustomerOtherInfo;
        let extraDetails = dbExtra;
        extraDetails.billSource = 'sms_parsed';
        extraDetails.updated_source = 'sms';
        extraDetails.updated_data_source = self.getOriginOfPayloadCurrentlyBeingProcessed(record);
        extraDetails.lastSuccessBFD = _.get(dbRecord, 'bill_fetch_date', null);
        extraDetails.billFetchDate = MOMENT(record.smsTimeStamp).isValid() ? MOMENT(record.smsTimeStamp).format('YYYY-MM-DD HH:mm:ss') : MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            extraDetails.lastDueDt = _.get(dbRecord, 'due_date', null);
        extraDetails.lastBillDt = _.get(dbRecord, 'bill_date', null);
        extraDetails.lastAmount = _.get(dbRecord, 'amount', null);
        if (_.get(record, 'isRuSmsParsing', false) == true) {
            _.set(extraDetails, 'isRuSmsParsing', true);
        }
        if (_.get(record, 'isDwhSmsParsing', false) == true) {
            _.set(extraDetails, 'isDwhSmsParsing', true)
        } else if (_.get(record, 'isDwhSmsParsingRealtime', false) == true) {
            _.set(extraDetails, 'isDwhSmsParsingRealtime', true)
        }
        if (_.get(record, 'partialRecordFound', false)) {
            _.set(extraDetails, 'source_subtype_2', 'PARTIAL_BILL');
        } else {
            _.set(extraDetails, 'source_subtype_2', 'FULL_BILL');
        }
        if (_.get(extraDetails, 'errorCounters', null)) {
            extraDetails.errorCounters = {};
        }
        custInfoValues.msgId = _.get(record, 'msgId', '');
        custInfoValues.sms_id = _.get(record, 'sms_id', '');
        custInfoValues.sms_date_time = _.get(record, 'sms_date_time', '');
        custInfoValues.sender_id = _.get(record, 'sender_id', '');
        custInfoValues.dwh_classId = _.get(record, 'dwh_classId', null);
        let billsData = {
            user_data: record.user_data,
            nextBillFetchDate: record.nextBillFetchDate,
            billFetchDate: extraDetails.billFetchDate,
            commonAmount: record.amount,
            commonDueDate: record.dueDate,
            rechargeNumber: record.rechargeNumber,
            billDate: record.billDate,
            productId: record.productId,
            commonStatus: record.status,
            customerId: record.customerId,
            customerMobile: _.get(record, 'smsReceiver', null),
            operator: record.operator,
            circle: record.circle,
            service: record.service,
            gateway: record.gateway,
            retryCount: 0,
            reason: null,
            paytype: _.get(record, 'paytype', null),
            customerEmail: _.get(record, 'customerEmail', null),
            service_id: _.get(record, 'service_id', 0),
            is_automatic: _.get(record, 'is_automatic', 0),
            msgId: _.get(record, 'msgId', ''),
            extra: JSON.stringify(extraDetails, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            }),
            customerOtherInfo: JSON.stringify(custInfoValues, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            })
        };
        return billsData;
    }

    /**
     * Returns active users for which smsparsing data will be updated
     * @param {*} dbRecords 
     */
    getActiveRecords(records) {
        let activeRecords = 0;
        for (let record of records) {
            if (record.status != _.get(this.config, 'COMMON.bills_status.DISABLED', 7) && record.status != _.get(this.config, 'COMMON.bills_status.NOT_IN_USE', 13)) {
                activeRecords++;
            }
        }
        return activeRecords;
    }

    /**
    * sorting DB data based on due date in descending order
    * [Use case: If some record have status=13 or 7 then code gets wrong information that we do not have latest billing cycle]
    * @param {*} data 
    */
    getSortedDbData(dbRecords) {

        dbRecords.sort(function (record1, record2) {
            let isValidR1date = MOMENT(record1.due_date).isValid(false);
            let isValidR2date = MOMENT(record2.due_date).isValid(false);

            if (isValidR1date && isValidR2date) {
                let daysDiff = MOMENT(record1.due_date).diff(record2.due_date, 'days');
                if (daysDiff > 0) return -1;
                else return 1;
            } else if (isValidR1date) {
                return -1;
            } else if (isValidR2date) {
                return 1;
            } else {
                return 0;
            }
        });

        return dbRecords;
    }

    getNextBillFetchDate(record) {
        let self = this;
        let billDateBasedGateways = _.get(self.config, 'SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS', []);
        let dateToBeUsed = billDateBasedGateways.indexOf(record.operator) > -1 && record.billDate ? record.billDate : record.dueDate;

        if (billDateBasedGateways.indexOf(record.operator) > -1 && !record.billDate) {
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:GENERIC_SMS_PARSING",
                `SERVICE:${_.get(record, 'category', null)}`,
                'STATUS:BILLDATE_ABSENT_BILLDATEBASEDGATEWAY',
                "OPERATOR:" + record.operator
            ]);
        }

        let nextBillFetchDate = self.billSubscriber.getFirstBillFetchInterval(record.operator) < 0 ? MOMENT(dateToBeUsed).add(Math.abs(Number(self.billSubscriber.getFirstBillFetchInterval(record.operator))), 'months') : MOMENT(dateToBeUsed).add(self.billSubscriber.getFirstBillFetchInterval(record.operator), 'days');
        if (nextBillFetchDate < MOMENT()) {
            self.L.error(`getNextBillFetchDate:: Change NBFD, currently set in past debugKey: ${record.debugKey} NBFD: ${nextBillFetchDate}`);
            nextBillFetchDate = MOMENT().add(1, 'days');
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:GENERIC_SMS_PARSING",
                `SERVICE:${_.get(record, 'category', null)}`,
                'STATUS:NBFD_SETTING_IN_PAST',
                "OPERATOR:" + record.operator
            ]);
        }
        return nextBillFetchDate.format('YYYY-MM-DD HH:mm:ss');
    }

    getServiceCategoryFromRecord(record) {
        let key = _.get(record, 'category', null) ? _.get(record, 'category', null).toLowerCase() : null;
        return key;
    }

    /**
    * @param {*} record 
    */

    publishToAutomaticSync(done, record) {
        let self = this,
            dbData = _.get(record, 'dbData', []);
        self.L.log('9. publishToAutomaticSync:: starting publishToAutomaticSync');

        if (!record.service || !record.operator || !record.rechargeNumber || !record.productId) {
            self.L.critical('publishInKafka :: invalid inputs ', record.service, record.operator, record.rechargeNumber, record.productId);
            return done('invalid inputs');
        }

        let publishedInAutomaticSync = false;
        ASYNC.eachLimit(dbData, 1, (dataRow, cb) => {
            let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
            if (dataRow.status == 13 || dataRow.status == 7) {
                self.L.log('publishInKafka', `Skipping pulish to : AUTOMATIC_SYNC_TOPIC for ${record.debugKey}, dbRow::${dbDebugKey} | due to inactice/old record`);
                return cb();
            }

            let billsData = record.billsData;

            dataRow.due_date = MOMENT(record.dueDate, 'YYYY-MM-DD HH:mm:ss').startOf('day').format('YYYY-MM-DD HH:mm:ss');
            if (record.is_automatic && record.is_automatic !== 0) {
                dataRow.is_automatic = record.is_automatic;
            }
            dataRow.bill_date = record.billDate ? MOMENT(record.billDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : null;
            dataRow.amount = record.amount;
            dataRow.bill_fetch_date = _.get(billsData, 'billFetchDate', null);
            dataRow.next_bill_fetch_date = _.get(billsData, 'nextBillFetchDate', null);
            dataRow.status = _.get(billsData, 'commonStatus', 0);
            dataRow.extra = _.get(billsData, 'extra', null);
            dataRow.updated_at = MOMENT().format('YYYY-MM-DD HH:mm:ss');
            dataRow.customerOtherInfo = _.get(billsData, 'customerOtherInfo', null);

            let row = self.commonLib.mapBillsTableColumns(dataRow);
            row.billGen = true;
            row.source = "postpaidBillFetchRealtime";
            row.machineId = OS.hostname();
            if (_.get(row, 'is_automatic', 0) !== 0 && _.get(row, 'is_automatic', 0) !== 5 && publishedInAutomaticSync == false) {
                publishedInAutomaticSync = true;
                self.parent.kafkaPublisher.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.AUTOMATIC_SYNC.AUTOMATIC_SYNC_TOPIC', ''),
                    messages: JSON.stringify(row),
                    key: _.get(record, 'rechargeNumber', '')
                }], function (error) {
                    if (error) {
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE: GENERIC_SMS_PARSING",
                            `SERVICE:${_.get(record, 'category', null)}`,
                            'STATUS:ERROR',
                            'TYPE:KAFKA_PUBLISH',
                            'TOPIC:AUTOMATIC_SYNC',
                            "OPERATOR:" + record.operator,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                            `APP_VERSION:${_.get(record, 'appVersion', null)}`
                        ]);
                        self.L.critical('Error while publishing message in Kafka - MSG:- ' + JSON.stringify(row), error);
                    } else {
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:GENERIC_SMS_PARSING",
                            `SERVICE:${_.get(record, 'category', null)}`,
                            'STATUS:PUBLISHED',
                            'TYPE:KAFKA_PUBLISH',
                            'TOPIC:AUTOMATIC_SYNC',
                            "OPERATOR:" + record.operator,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                            `APP_VERSION:${_.get(record, 'appVersion', null)}`
                        ]);
                        self.L.log('Message published successfully in Kafka', ' on topic AUTOMATIC_SYNC', JSON.stringify(row));
                    }
                    return cb();
                }, [200, 800]);
            }
            else {
                self.L.log('Skipping publish in Kafka', ' on topic AUTOMATIC_SYNC', 'Due to is_automatic = 0');
                return cb();
            }
        }, (error, res) => {
            if (error) {
                self.L.error("postpaidSmsParsing :: publishKafka ", "Error occured", error);
            }
            return done(error);
        });
    }

    async publishInKafka(done, record, action,kafkaTopic,configurations) {
        let self = this;
        
        ASYNC.parallel([
            function (cb) {
                if (configurations.KAFKA_AUTOMATIC_ENABLE && configurations.KAFKA_AUTOMATIC_ENABLE == 1) {
                    self.publishToAutomaticSync(function (err) {
                        cb(err);
                    }, record)

                }
                else {
                    cb();
                }

            },
            function (cb) {
                if ((configurations.KAFKA_REMINDER_BILL_FETCH_ENABLE && configurations.KAFKA_REMINDER_BILL_FETCH_ENABLE ==1) || (configurations.KAFKA_REMINDER_BILL_FETCH_REALTIME_ENABLE && configurations.KAFKA_REMINDER_BILL_FETCH_REALTIME_ENABLE==1)) {
                    self.publishInBillFetchKafka(function (err) {
                        cb(err)
                    }, record, configurations)
                }
                else {
                    cb();
                }
            },
            function (cb) {
                if (configurations.KAFKA_CT_ENABLE && configurations.KAFKA_CT_ENABLE==1) {
                    self.publishCtEvents(function (err) {
                        cb(err)
                    }, record)
                }
                else {
                    cb();
                }

            },
        ], function (error) {
            if (error) {
                self.L.error('Error occurred during parallel tasks:', error);
            }
            done(error);
        });
    }

    /**
     * @param {*} record 
     */

    async publishCtEvents(done, record) {
        let self = this;
        self.L.log(`11. publishCtEvents:: Record Category - ${record.category}`);
        const productId = _.get(record, 'productId', 0);
        let dbData = _.get(record, 'dbData', []);
        ASYNC.eachLimit(dbData, 3, (dataRow, cb) => {
            let eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'BILLGEN'], 'reminderBillGen');

            let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
            if ((dataRow.status == 13 || dataRow.status == 7)) {
                self.L.log('publishInKafka', `Skipping pulish CT for ${record.debugKey}, dbRow::${dbDebugKey} | due to inactice record`);
                return cb();
            }

            if (self.commonLib.isCTEventBlocked(eventName)) {
                self.L.info(`Blocking CT event ${eventName}`)
                return cb()
            }

            let billsData = record.billsData;
            dataRow.due_date = MOMENT(record.dueDate, 'YYYY-MM-DD HH:mm:ss').startOf('day').format('YYYY-MM-DD HH:mm:ss');

            if (dataRow.notification_status == 0) {
                self.L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${dataRow.notification_status} debugKey::`, dbDebugKey);
                return cb();
            }

            if (dataRow.notification_status == null)
                dataRow.notification_status = 1
            dataRow.bill_date = record.billDate ? MOMENT.utc(record.billDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : null;
            dataRow.amount = record.amount;
            dataRow.bill_fetch_date = MOMENT.utc(_.get(billsData, 'billFetchDate', null)).format('YYYY-MM-DD HH:mm:ss');
            dataRow.next_bill_fetch_date = MOMENT.utc(_.get(billsData, 'nextBillFetchDate', null)).format('YYYY-MM-DD HH:mm:ss');
            dataRow.status = _.get(billsData, 'commonStatus', 0);
            dataRow.extra = _.get(billsData, 'extra', null);
            dataRow.updated_at = MOMENT.utc().format('YYYY-MM-DD HH:mm:ss');
            dataRow.customerOtherInfo = _.get(billsData, 'customerOtherInfo', null);
            dataRow.rtspClassId = _.get(record, 'rtspClassId', null);
            dataRow.dwhClassId = _.get(record, 'dwhClassId', null);

            ASYNC.waterfall([
                next => {
                    self.commonLib.getRetailerData((error) => {
                        if (error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, dataRow.customer_id, dataRow);
                },
                next => {
                    self.commonLib.getCvrData((error) => {
                        if (error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, productId, dataRow);
                },
                next => {
                    let mappedData = self.reminderUtils.createCTPipelinePayload(dataRow, eventName, dbDebugKey);
                    let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                    self.parent.ctKafkaPublisher.publishData([{
                        topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                        messages: JSON.stringify(mappedData)
                    }], (error) => {
                        if (error) {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:GENERIC_SMS_PARSING",
                                `SERVICE:${_.get(record, 'category', null)}`,
                                'STATUS:ERROR',
                                "TYPE:KAFKA_PUBLISH",
                                "TOPIC:CT_EVENTS",
                                "OPERATOR:" + dataRow.operator,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record, 'appVersion', null)}`
                            ]);
                            self.L.critical('postpaidSmsParsing :: publishCtEvents', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(clonedData), error);
                        } else {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:GENERIC_SMS_PARSING",
                                `SERVICE:${_.get(record, 'category', null)}`,
                                'STATUS:PUBLISHED',
                                "TYPE:KAFKA_PUBLISH",
                                "TOPIC:CT_EVENTS",
                                "OPERATOR:" + dataRow.operator,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record, 'appVersion', null)}`,
                                `EVENT_NAME:${eventName}`
                            ]);
                            self.L.log('postpaidSmsParsing :: publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                        }
                        return next(error);
                    }, [200, 800]);
                }
            ], (err) => {
                if (err)
                    self.L.log('postpaidSmsParsing :: publishCtEvents', 'Error while publishing message in Kafka: ', err)
                return cb(err)
            })
        }, (error, res) => {
            if (error) {
                self.L.error("postpaidSmsParsing :: publishCtEvents ", "Error occured", error);
            }
            return done(error);
        });
    }



    createRecordForAnalytics(record, source_subtype_2, user_type) {
        let recordForAnalytics = {},
            self = this;
        recordForAnalytics.source = self.smsParsingBillsDwhRealtime ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH";
        recordForAnalytics.source_subtype_2 = source_subtype_2;
        recordForAnalytics.user_type = user_type;
        recordForAnalytics.customer_id = _.get(record, 'cId', null);
        recordForAnalytics.service = (self.getServiceCategoryFromRecord(record)).toLocaleLowerCase();
        recordForAnalytics.recharge_number = _.get(record, 'rechargeNumber', null);
        recordForAnalytics.operator = _.get(record, 'smsOperator', null);
        recordForAnalytics.due_amount = _.get(record, 'dueAmt', null);
        recordForAnalytics.additional_info = null;
        recordForAnalytics.sms_id = _.get(record, 'msg_id', null);
        recordForAnalytics.paytype = "postpaid";
        recordForAnalytics.updated_at = _.get(record, 'updatedAt', null);
        recordForAnalytics.sender_id = _.get(record, 'smsSenderID', null);
        recordForAnalytics.sms_date_time = self.getEpochminiToTimestampString(_.get(record, 'smsDateTime', null), recordForAnalytics);
        recordForAnalytics.sms_class_id = _.get(record, 'level_2_category', null);
        recordForAnalytics.due_date = _.get(record, 'dueDate', utility.getFilteredDate(_.get(record, 'telecom_details.due_date', _.get(record, 'dueDate', null))).value);
        recordForAnalytics.bill_date = _.get(record, 'billDate', utility.getFilteredDate(_.get(record, 'telecom_details.bill_date', _.get(record, 'billDate', null))).value || MOMENT(record.smsDateTime));
        recordForAnalytics.bill_fetch_date = _.get(record, 'billFetchDate', MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        return recordForAnalytics;
    }


    createRecordForAnalytics1(record, source_subtype_2, user_type) {
        let recordForAnalytics = {},
            self = this;
        recordForAnalytics.source = self.smsParsingBillsDwhRealtime ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH";
        recordForAnalytics.source_subtype_2 = source_subtype_2;
        recordForAnalytics.user_type = user_type;
        recordForAnalytics.customer_id = _.get(record, 'customerId', null);
        recordForAnalytics.service = (_.get(record, 'category', null)).toLowerCase();
        recordForAnalytics.recharge_number = _.get(record, 'rechargeNumber', null); 
        recordForAnalytics.operator = _.get(record, 'operator', null);
        recordForAnalytics.due_amount = _.get(record, 'amount', null);
        recordForAnalytics.additional_info = null;
        recordForAnalytics.sms_id = _.get(record, 'msgId', null);
        recordForAnalytics.paytype = _.get(record, 'paytype', null);
        recordForAnalytics.updated_at = _.get(record, 'updatedAt', null);
        recordForAnalytics.sender_id = _.get(record, 'sender_id', null);
        recordForAnalytics.sms_date_time = self.getEpochminiToTimestampString(_.get(record, 'smsDateTime', null), recordForAnalytics);
        recordForAnalytics.sms_class_id = _.get(record, 'dwh_classId', null);
        recordForAnalytics.due_date = _.get(record, 'dueDate', null);
        recordForAnalytics.bill_date = _.get(record, 'billDate', null);
        recordForAnalytics.bill_fetch_date = _.get(record, 'billFetchDate', MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        return recordForAnalytics;
    }

    async saveAndPublishBillFetchAnalyticsData(record, error, cb, cbParam) {
        let self = this;
        try {
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(record, error);
        } catch (e) {
            self.L.critical('saveAndPublishBillFetchAnalyticsData :: Error while publishing in analytics cassandra and kafka');
        }
        if (cbParam) {
            cb(error, cbParam);
        } else {
            cb(error);
        }
    }

    async saveAndPublishBillFetchAnalyticsDataWithoutError(record, error, cb) {
        let self = this;
        try {
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(record, error);
        } catch (e) {
            self.L.critical('saveAndPublishBillFetchAnalyticsData :: Error while publishing in analytics cassandra and kafka');
        }
        cb();
    }

    getEpochminiToTimestampString(time, recordForAnalytics) {

        if (_.get(recordForAnalytics, "service", null) == 'mobile') {
            return time;
        }
        else {
            if (typeof time == "number" || (Number(time) != NaN && Number(time) > 0)) {
                return MOMENT(Number(time)).format('YYYY-MM-DD HH:mm:ss');
            }
            return MOMENT().format('YYYY-MM-DD HH:mm:ss')
        }
    }

    saveForAnalyticsInCassandraAndKafka(record) {
        let self = this;
        if (self.saveForAnalyticsInCassandraDbAndKafka) {
            let classId = _.get(record, 'dwh_classId', null);
            if (!classId) _.get(record, 'level_2_category', null);

            if (classId) {
                classId = (typeof classId == "string") ? classId : `${classId}`;
                let allowedClassIdsForAnalytics = _.get(self.config, ['DYNAMIC_CONFIG', 'SMSPARSING', 'AllowedDwhClassIdsForAnalytics', 'classIds'], ["1", "5", "6", "8", "11"]);
                if (allowedClassIdsForAnalytics.includes(classId)) return true;
            }
        }
        return false;
    }

}
export default genericSmsParsing;
