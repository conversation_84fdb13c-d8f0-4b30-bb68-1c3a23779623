/**
 * BillDataValidator - Validates bill data for NonPaytmBills
 *
 * This class is responsible for:
 * - Validating bill data
 * - Checking for mandatory fields
 * - Validating amount limits
 * - Validating due dates
 */

import _ from 'lodash';
import MOMENT from 'moment';
import utility from '../../lib';

// Constants
const MAX_AMOUNT = 2147483647;
const MIN_AMOUNT = -2147483648;

class BillDataValidator {
    /**
     * Create a new BillDataValidator
     * @param {Object} options - Configuration options
     * @param {Object} options.config - Configuration object
     * @param {Object} options.L - Logger instance
     */
    constructor(options) {
        this.config = options.config;
        this.L = options.L;
    }

    /**
     * Validate bill data
     * @param {Object} billsKafkaRow - Bill data
     * @param {string} dbEvent - Database event
     * @returns {Object} Validation result
     */
    validateBillsData(billsKafkaRow, dbEvent) {
        let self = this;
        // Check if dbEvent is present in new function
        const dbEventValidationResult = self._validateDbEvent(dbEvent);
        if (!dbEventValidationResult.isValid) {
            return dbEventValidationResult;
        }
        // Check mandatory fields
        const mandatoryFieldsResult = self._checkMandatoryFields(billsKafkaRow);
        if (!mandatoryFieldsResult.isValid) {
            return mandatoryFieldsResult;
        }
        // Validate amount limits
        const amountValidationResult = self._validateAmountLimits(billsKafkaRow);
        if (!amountValidationResult.isValid) {
            return amountValidationResult;
        }
        return { isValid: true };
    }

    /**
     * Check mandatory fields in bill data
     * @param {Object} billsKafkaRow - Bill data
     * @returns {Object} Validation result
     */
    _checkMandatoryFields(billsKafkaRow) {
        const mandatoryParams = ['rechargeNumber', 'operator', 'service', 'paytype', 'productId', 'customerId', 'dbEvent'];
        const fieldsNotPresent = [];

        for (const field of mandatoryParams) {
            if (!billsKafkaRow[field]) {
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:NON_PAYTM_BILLS",
                    'STATUS:MANDATORY_FIELDS_ABSENT',
                    "FIELD:" + field,
                    `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`,
                    `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`,
                    `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`
                ]);
                fieldsNotPresent.push(field);
            }
        }

        if (fieldsNotPresent.length > 0) {
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:NON_PAYTM_BILLS",
                'STATUS:FIELDS_MISSING',
                `ORIGIN:${_.get(billsKafkaRow, 'source', "NO_SOURCE")}`,
                `SERVICE:${_.get(billsKafkaRow, 'service', "NO_SERVICE")}`,
                `OPERATOR:${_.get(billsKafkaRow, 'operator', 'NO_OPERATOR')}`
            ]);

            const errorMessage = `Mandatory fields not present:: Missing params:: ${fieldsNotPresent.join(',')}`;
            this.L.log('Error in validateKafkaRecord', `Mandatory fields not present:: ${billsKafkaRow.loggingDebugKey} Missing params:: ${fieldsNotPresent.join(',')}`);

            return { isValid: false, error: errorMessage };
        }

        return { isValid: true };
    }

    /**
     * Validate amount limits
     * @param {Object} billsKafkaRow - Bill data
     * @returns {Object} Validation result
     */
    _validateAmountLimits(billsKafkaRow) {
        if (_.get(billsKafkaRow, 'amount', 0) > MAX_AMOUNT) {
            this.L.error("Record rejected due to amount exceeding the maximum limit");
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:NON_PAYTM_BILLS",
                'STATUS:ERROR',
                'TYPE:MAX_AMOUNT_LIMIT',
                'SOURCE:WRITE_BATCH_RECORDS',
                'SERVICE:' + _.get(billsKafkaRow, 'service', "NO_SERVICE")
            ]);
            return { isValid: false, error: 'Amount exceeds the maximum limit' };
        }

        if (_.get(billsKafkaRow, 'amount', 0) < MIN_AMOUNT) {
            this.L.error("Record rejected due to amount exceeding the minimum limit");
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:NON_PAYTM_BILLS",
                'STATUS:ERROR',
                'TYPE:MIN_AMOUNT_LIMIT',
                'SOURCE:WRITE_BATCH_RECORDS',
                'SERVICE:' + _.get(billsKafkaRow, 'service', "NO_SERVICE")
            ]);
            return { isValid: false, error: 'Amount exceeds the minimum limit' };
        }

        return { isValid: true };
    }

    _validateDbEvent(dbEvent) {
        if (!dbEvent || dbEvent === '') {
            return { isValid: false, error: 'db event is missing' };
        }
        return { isValid: true };
    }
}

export default BillDataValidator;
