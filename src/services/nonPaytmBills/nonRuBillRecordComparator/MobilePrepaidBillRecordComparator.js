import _ from 'lodash';
import BaseBillRecordComparator from './BaseBillRecordComparator.js';
import MOMENT from 'moment';
import utility from '../../../lib';

class MobilePrepaidBillRecordComparator extends BaseBillRecordComparator {
    constructor(options) {
        super(options);
        this.nonPaytmBillsModel = options.nonPaytmBillsModel;
        this.L = options.L;
    }

    async process(billsKafkaRow, existingRecord) {
        try {
            this.L.log('MobilePrepaidBillRecordComparator',
                `Processing record with mobile prepaid strategy for service: ${billsKafkaRow.service}`);
            await this.postProcessExistingRecord(billsKafkaRow, existingRecord);
        } catch (error) {
            this.L.error('MobilePrepaidBillRecordComparator',
                `Failed to process record: ${error}`);
            throw error;
        }
    }

    async getTotalRecordsToProcess(billsKafkaRow, existingRecords) {
        const self = this;
        self.L.log("getTotalRecordsToProcess", `Getting total records to process for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
        try {
            let smsParsedCustId = null,
                isSmsParsedCustIdPresentInSql = false,
                listOfExistingCustIds = [],
                listOfAirtelPrepaidCustIds = [];

            isSmsParsedCustIdPresentInSql = _.get(billsKafkaRow, 'isSmsParsedCustIdPresentInSql', false) || (billsKafkaRow.isDuplicateCANumberOperator && billsKafkaRow.alternateCAExistsInDB);
            smsParsedCustId = _.get(billsKafkaRow, 'customerId', null);
            // Create a map of customer IDs to existing records
            const existingRecordsMap = new Map();
            if (_.get(billsKafkaRow, 'isRealTimeDataExhausted', false)) {
                //filter the existing records based on smsParsedCustId
                existingRecords = existingRecords.filter(record => record.customer_id == smsParsedCustId);
                if (existingRecords.length > 0)
                    existingRecordsMap.set(smsParsedCustId, existingRecords[0]);
                else
                    existingRecordsMap.set(smsParsedCustId, []);
            }

            existingRecords.forEach(record => {
                existingRecordsMap.set(record.customer_id, record);
            });
            /**
             * processedRecords -> sms parsed cust_id -> cust_id1
             * list_of_cust_ids -> [cust_id2, cust_id3, cust_id4]
             * 
             * existingRecords -> cust_id5, cust_id6, cust_id7, cust_id8
             * 
             * 
             * Existing records -> cust_id5, cust_id6, cust_id7, cust_id8
             * new creations -> cust_id2, cust_id3, cust_id4
             * 
             * nothing to be done on cust_id1 -> already present in sql
             * 
             */
            listOfAirtelPrepaidCustIds = _.get(billsKafkaRow, ['airtelPrepaidPublisherDetails', 'list_of_other_customer_ids'], []);
            listOfExistingCustIds = Array.from(existingRecordsMap.keys());
            if (listOfExistingCustIds.length == 0 && listOfAirtelPrepaidCustIds.length == 0 && isSmsParsedCustIdPresentInSql) {
                self.L.log("No existing records found, returning empty array");
                return [];
            }
            let extraForAirtelPrepaid = JSON.stringify({
                "created_source": "AIRTEL_PREPAID_PUBLISHER_MIGRATED",
                "updated_source": "AIRTEL_PREPAID_PUBLISHER_MIGRATED",
                "updated_data_source": "AIRTEL_PREPAID_PUBLISHER_MIGRATED"
            });
            listOfAirtelPrepaidCustIds.forEach(cust_id => {
                if (!existingRecordsMap.has(cust_id)) {
                    existingRecordsMap.set(cust_id, []);
                }
            });
            // Add new customer ID to map if not present
            if (!isSmsParsedCustIdPresentInSql && !existingRecordsMap.has(smsParsedCustId)) {
                existingRecordsMap.set(smsParsedCustId, []);
            }
            return existingRecordsMap;
        } catch (error) {
            self.L.error("Error while getting total records to process with error :", error);
            throw error;
        }
    }

    async postProcessExistingRecord(billsKafkaRow, existingRecord) {
        const self = this, dataExhaustProcessed = _.get(billsKafkaRow, 'isRealTimeDataExhausted', false);
        //check if record is to be rejected
        this.shouldRejectRecord(billsKafkaRow, existingRecord);
        //process for data exhaust cases
        if (dataExhaustProcessed) {
            this.processForDataExhaust(billsKafkaRow, existingRecord);
        }
    }

    /**
     * Process for data exhaust cases
     * @param {Object} billsKafkaRow - Bill data
     * @param {Object} existingRecord - Existing record data
     * @returns {Object} Processed bill data
     */
    processForDataExhaust(billsKafkaRow, existingRecord) {
        const self = this;
        this.L.log('processForDataExhaust', `Processing for data exhaust for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
        try {
            billsKafkaRow.productId = existingRecord.product_id;
            billsKafkaRow.operator = existingRecord.operator;
            billsKafkaRow.bill_fetch_date = existingRecord.bill_fetch_date;
            billsKafkaRow.amount = existingRecord.amount;
            billsKafkaRow.paytype = existingRecord.paytype;
            billsKafkaRow.service = existingRecord.service;
            billsKafkaRow.circle = existingRecord.circle;
            billsKafkaRow.customer_mobile = existingRecord.customer_mobile;
            billsKafkaRow.customer_email = existingRecord.customer_email;
            billsKafkaRow.status = existingRecord.status;
            billsKafkaRow.userData = existingRecord.user_data;
            billsKafkaRow.billDate = existingRecord.bill_date;
            billsKafkaRow.notificationStatus = existingRecord.notification_status;
            billsKafkaRow.dueDate = existingRecord.due_date;
            let existingCustomerOtherInfo = utility.parseCustomerOtherInfo(_.get(existingRecord, 'customer_other_info', '{}')),
                customerOtherInfo = utility.parseCustomerOtherInfo(_.get(billsKafkaRow, 'customerOtherInfo', '{}'));
            existingCustomerOtherInfo.sms_id = _.get(customerOtherInfo, "sms_id");
            existingCustomerOtherInfo.sms_date_time = _.get(customerOtherInfo, "sms_date_time");
            existingCustomerOtherInfo.smsDateTime = _.get(customerOtherInfo, "smsDateTime");
            existingCustomerOtherInfo.msgId = _.get(customerOtherInfo, "msgId");
            billsKafkaRow.customerOtherInfo = utility.stringifyCustomerOtherInfo(existingCustomerOtherInfo);
            let currentExtra = utility.parseExtra(_.get(billsKafkaRow, 'extra', '{}'));
            let existingRecordExtra = utility.parseExtra(_.get(existingRecord, 'extra', '{}'));
            existingRecordExtra.isRealTimeDataExhausted = true;
            existingRecordExtra.is_data_exhaust = true;
            existingRecordExtra.isDataExhausted = true;
            existingRecordExtra.data_exhaust_value = currentExtra.data_exhaust_value;
            existingRecordExtra.data_exhaust_date = currentExtra.data_exhaust_date;
            existingRecordExtra.updated_data_source = _.get(currentExtra, "updated_data_source");
            billsKafkaRow.extra = utility.stringifyExtra(existingRecordExtra);
        } catch (error) {
            self.L.error(`processExistingRecord`, `Failed with error ${error}`);
            return null;
        }
        this.L.log('postProcessExistingRecord', `Post processed existing record for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);

        return billsKafkaRow;
    }

    shouldRejectRecord(billsKafkaRow, existingRecord) {
        let self = this;
        let existingExtra = utility.parseExtra(_.get(existingRecord, 'extra', '{}')),
            customerOtherInfo = utility.parseCustomerOtherInfo(_.get(billsKafkaRow, 'customerOtherInfo', '{}')),
            dbDueDate = _.get(existingRecord, 'due_date', null),
            DUE_DATE_DIFF = _.get(this.config, ['DYNAMIC_CONFIG', 'PARTIAL_BILL_THRESHOLD', 'MOBILE_PARTIAL_BILL', 'IGNORE_DAYS_THRESHOLD'], 7),
            dwhClassId = _.get(customerOtherInfo, 'dwh_classId', null),
            partialBillState = _.get(billsKafkaRow, 'partialBillState', null),
            realTimeDataExhausted = _.get(billsKafkaRow, 'isRealTimeDataExhausted', false),
            previousPaytype = _.get(existingRecord, 'paytype', null);
        if (billsKafkaRow.isValaidationSync && existingExtra.created_source != 'validationSync' && !billsKafkaRow.isValidityExpired) {
            // if record already present by sms, do not update by validation sync
            this.L.log('shouldRejectRecord', `Record with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)} is to be rejected`);
            throw new Error("Record is to be rejected as due to validation sync");
        } else if (partialBillState && dbDueDate && MOMENT(dbDueDate).diff(MOMENT(), 'days') >= -1 * Number(DUE_DATE_DIFF)) {
            this.L.log('shouldRejectRecord', `Record with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)} is to be rejected`);
            throw new Error("Record is to be rejected as due to partial bill state");
        } else if (realTimeDataExhausted && previousPaytype == "postpaid") {
            this.L.log('shouldRejectRecord', `Record with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)} is to be rejected as postpaid bill not updated by data exhaust bill`);
            throw new Error("postpaid bill not updated by data exhaust bill");
        } else {
            this.L.log('shouldRejectRecord', `Record with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)} is not to be rejected`);
        }
    }

    /**
                 * Validates if the bill should be updated based on due date and amount changes
                 * @param {Object} billsKafkaRow - New bill data
                 * @param {Object} existingRecord - Existing database record
                 * @returns {Promise<boolean>} True if bill should be updated
                 * @private
                 */
    async _shouldUpdateBillBasedOnDueDateAndAmount(billsKafkaRow, existingRecord) {
        try {
            const currentDueDate = _.get(billsKafkaRow, 'dueDate', null);
            const existingDueDate = _.get(existingRecord, 'due_date', null);
            const currentAmount = _.get(billsKafkaRow, 'amount', null);
            const existingAmount = _.get(existingRecord, 'due_amount', null);
            const isRealTimeDataExhausted = _.get(billsKafkaRow, 'isRealTimeDataExhausted', false);
            // Consider due date changed if either value is null or they differ
            const hasDueDateChanged = currentDueDate && (!existingDueDate ||
                MOMENT(currentDueDate).utc().startOf('day').diff(MOMENT(existingDueDate).utc().startOf('day'), 'days') !== 0);
            // Consider amount changed if either value is null or they differ
            const hasAmountChanged = currentAmount && (!existingAmount || currentAmount !== existingAmount);
            if (isRealTimeDataExhausted) {
                this.L.log('shouldUpdateBill', `Skipping the bill for debugKey ${_.get(billsKafkaRow, 'debugKey', null)} as it is real time data exhausted`);
                return billsKafkaRow;
            }
            else if (hasDueDateChanged || hasAmountChanged) {
                this.L.log('shouldUpdateBill', `Updating the bill for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}`);
                return billsKafkaRow;
            } else {
                // Log as info instead of error since this is expected behavior for duplicate records
                this.L.log('handleCommonDueDateAndAmountFeatures', `Skipping duplicate record - currentDueDate and existingDueDate and currentAmount and existingAmount are same for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}`);
                throw new Error(`currentDueDate and existingDueDate and currentAmount and existingAmount are same`);
            }
        } catch (error) {
            this.L.error('handleCommonSMSParsedFeatures', `Failed to process common SMS features: ${error} for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}`);
            throw error;
        }
    }


    makeExistingRecords(cust_id, billsKafkaRow, extra = null) {
        return {
            customer_id: cust_id,
            recharge_number: billsKafkaRow.rechargeNumber,
            operator: billsKafkaRow.operator,
            service: billsKafkaRow.service,
            paytype: billsKafkaRow.paytype,
            product_id: billsKafkaRow.productId,
            notification_status: 1,
            extra: extra,
            dummyRecord: true
        }
    }

    /**
     * Get the service type this strategy handles
     * @returns {string} Service type
     */
    getServiceType() {
        return 'mobile';
    }

    /**
     * Get the paytype this strategy handles    
     * @returns {string|null} Paytype or null if not applicable
     */
    getPaytype() {
        return 'prepaid';
    }
}

export default MobilePrepaidBillRecordComparator;